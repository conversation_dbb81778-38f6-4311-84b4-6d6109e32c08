import asyncHand<PERSON> from "../middlewares/asyncHandler.js";
import NotificationModel from "../models/notificationModel.js";

const NotificationController = {
  // Get all notifications
  getAll: asyncHandler(async (req, res) => {
    const notifications = await NotificationModel.getAll();
    res.json({
      success: true,
      total: notifications.length,
      data: notifications,
    });
  }),

  // Get notification by ID
  getById: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const notification = await NotificationModel.getById(id);
    
    if (!notification) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    res.json({
      success: true,
      data: notification,
    });
  }),

  // Get notifications by category
  getByCategory: asyncHandler(async (req, res) => {
    const { category } = req.params;
    const notifications = await NotificationModel.getByCategory(category);
    
    res.json({
      success: true,
      total: notifications.length,
      data: notifications,
    });
  }),

  // Get unread count
  getUnreadCount: asyncHandler(async (req, res) => {
    const count = await NotificationModel.getUnreadCount();
    res.json({
      success: true,
      unreadCount: count,
    });
  }),

  // Mark notification as read
  markAsRead: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const success = await NotificationModel.markAsRead(id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    res.json({
      success: true,
      message: "Notification marked as read",
    });
  }),

  // Mark all notifications as read
  markAllAsRead: asyncHandler(async (req, res) => {
    const affectedRows = await NotificationModel.markAllAsRead();
    
    res.json({
      success: true,
      message: `${affectedRows} notifications marked as read`,
      affectedRows,
    });
  }),

  // Delete notification
  delete: asyncHandler(async (req, res) => {
    const { id } = req.params;
    const success = await NotificationModel.delete(id);
    
    if (!success) {
      return res.status(404).json({
        success: false,
        message: "Notification not found",
      });
    }

    res.json({
      success: true,
      message: "Notification deleted successfully",
    });
  }),

  // Delete all notifications
  deleteAll: asyncHandler(async (req, res) => {
    const affectedRows = await NotificationModel.deleteAll();
    
    res.json({
      success: true,
      message: `${affectedRows} notifications deleted`,
      affectedRows,
    });
  }),

  // Create notification (for testing purposes)
  create: asyncHandler(async (req, res) => {
    const notification = await NotificationModel.create(req.body);

    res.status(201).json({
      success: true,
      message: "Notification created successfully",
      data: notification,
    });
  }),

  // Test endpoint to create a sample contact notification
  createTestContactNotification: asyncHandler(async (req, res) => {
    try {
      const testContact = {
        C_Id: Date.now(), // Use timestamp as fake ID
        user_name: 'Test User',
        user_email: '<EMAIL>',
        C_Title: 'Test Contact Form',
        C_Body: 'This is a test contact form submission to verify notifications work.',
        user_phone: '0701234567'
      };

      const notification = await NotificationModel.createContactNotification(testContact);

      res.status(201).json({
        success: true,
        message: "Test contact notification created successfully",
        data: notification,
      });
    } catch (error) {
      console.error('Error creating test notification:', error);
      res.status(500).json({
        success: false,
        message: "Failed to create test notification",
        error: error.message,
      });
    }
  }),
};

export default NotificationController;
