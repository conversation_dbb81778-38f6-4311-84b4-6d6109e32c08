# Debugging Contact Form Notifications

## Issue: New contact form submissions not showing in notifications

Let's debug this step by step:

## Step 1: Check Backend Logs

1. **Check your backend console** for any error messages when submitting a contact form
2. Look for messages like:
   - "Failed to create notification: [error message]"
   - Any database connection errors
   - Import/export errors

## Step 2: Test Database Connection

Run this command in your backend directory:
```bash
node scripts/debug-notifications.js
```

This will check:
- If the Notifications table exists
- Recent contact submissions
- If notifications are being created for contacts

## Step 3: Test Contact Form Submission

1. **Submit a contact form** at `http://localhost:3000/contact`
2. **Check the browser's Network tab** (F12 → Network):
   - Look for the POST request to `/api/v1/contact-us`
   - Check if it returns status 201 (success)
   - Look for any error responses

## Step 4: Test Notification API Directly

1. **Login as admin** first at `http://localhost:3000/signin`
2. **Open browser console** (F12 → Console)
3. **Run this JavaScript code** to test the notification API:

```javascript
// Get the auth token
const token = localStorage.getItem('token');
console.log('Token:', token ? 'Found' : 'Not found');

// Test fetching notifications
fetch('http://localhost:5432/api/v1/notifications', {
  headers: { 'Authorization': `Bearer ${token}` }
})
.then(response => response.json())
.then(data => {
  console.log('Notifications API response:', data);
})
.catch(error => {
  console.error('Error fetching notifications:', error);
});

// Test unread count
fetch('http://localhost:5432/api/v1/notifications/unread-count', {
  headers: { 'Authorization': `Bearer ${token}` }
})
.then(response => response.json())
.then(data => {
  console.log('Unread count API response:', data);
})
.catch(error => {
  console.error('Error fetching unread count:', error);
});
```

## Step 5: Check Frontend Console

1. **Open browser console** while on the admin page
2. **Look for error messages** related to notifications:
   - "Error fetching notifications"
   - "Error fetching unread count"
   - Network errors

## Step 6: Manual Database Check

If you have access to your MySQL database, run these queries:

```sql
-- Check if notifications table exists
SHOW TABLES LIKE 'Notifications';

-- Check recent notifications
SELECT * FROM Notifications ORDER BY created_at DESC LIMIT 10;

-- Check recent contacts
SELECT * FROM ContactUs ORDER BY C_Id DESC LIMIT 5;

-- Check if notifications exist for recent contacts
SELECT n.*, c.user_name, c.C_Title 
FROM Notifications n 
LEFT JOIN ContactUs c ON n.contact_id = c.C_Id 
WHERE n.category = 'contacts' 
ORDER BY n.created_at DESC;
```

## Common Issues and Solutions

### Issue 1: NotificationModel Import Error
**Symptoms:** Backend console shows "NotificationModel is not defined"
**Solution:** ✅ Already fixed - added import to contactUsController.js

### Issue 2: Database Table Missing
**Symptoms:** Database errors about table not existing
**Solution:** Run the table creation script:
```bash
node scripts/create-notifications-table.js
```

### Issue 3: Authentication Issues
**Symptoms:** 401/403 errors when fetching notifications
**Solution:** 
- Make sure you're logged in as admin
- Check if the token is valid
- Verify admin role permissions

### Issue 4: Frontend Not Refreshing
**Symptoms:** Notifications exist in database but not showing in UI
**Solution:**
- Check browser console for errors
- Try manually refreshing the page
- Check if the polling is working (should refresh every 30 seconds)

### Issue 5: CORS Issues
**Symptoms:** Network errors in browser console
**Solution:** Check if your backend CORS settings allow the frontend domain

## Quick Test Steps

1. **Submit a contact form**
2. **Wait 30 seconds** (for automatic refresh) or **refresh the admin page**
3. **Check the notification bell** in the admin header
4. **Visit** `http://localhost:3000/admin/notifications/contacts` directly

## If Still Not Working

Please check:
1. **Backend console output** when submitting a contact form
2. **Browser console errors** on the admin page
3. **Network tab** for failed API requests
4. **Database** to see if notifications are being created

Let me know what you find in these checks, and I can help you fix the specific issue!
