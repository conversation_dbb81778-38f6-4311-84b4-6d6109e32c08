import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';

const EditDrugPurchasePage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { language, translations } = useLanguage();

  const [formData, setFormData] = useState({
    drug_name: '',
    quantity: '',
    price_per_unit: '',
    supplier: '',
    purchase_date: '',
    notes: '',
    status: '',
  });
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [fetchError, setFetchError] = useState(null);
  const [purchases, setPurchases] = useState([]);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    const fetchPurchase = async () => {
      setLoading(true);
      setFetchError(null);
      try {
        const res = await fetch(`http://localhost:5432/api/v1/drug-purchases/${id}`);
        if (!res.ok) throw new Error('Failed to fetch purchase');
        const json = await res.json();
        if (!json.success || !json.data) throw new Error('Purchase not found');
        setFormData({
          drug_name: json.data.drug_name || '',
          quantity: json.data.quantity?.toString() || '',
          price_per_unit: json.data.price_per_unit?.toString() || '',
          supplier: json.data.supplier || '',
          purchase_date: json.data.purchase_date ? json.data.purchase_date.slice(0, 10) : '',
          notes: json.data.notes || '',
          status: json.data.status || '',
        });
      } catch (err) {
        setFetchError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchPurchase();
  }, [id]);

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);

  const allDrugsFromPurchases = purchases;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.drug_name.trim()) newErrors.drug_name = 'Drug name is required';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (!formData.price_per_unit || parseFloat(formData.price_per_unit) < 0) newErrors.price_per_unit = 'Price per unit must be a positive number';
    if (!formData.supplier.trim()) newErrors.supplier = 'Supplier is required';
    if (!formData.purchase_date) newErrors.purchase_date = 'Purchase date is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    if (!validateForm()) return;
    try {
      setLoading(true);
      const purchaseData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        price_per_unit: parseFloat(formData.price_per_unit),
        total_amount: parseInt(formData.quantity) * parseFloat(formData.price_per_unit),
        status: formData.status || 'Pending',
      };
      const response = await fetch(`http://localhost:5432/api/v1/drug-purchases/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(purchaseData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update purchase');
      }
      await response.json();
      alert('Purchase updated successfully!');
      navigate('/admin/drugs/purchases');
    } catch (error) {
      setSubmitError(error.message || 'Failed to update purchase. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const totalAmount = formData.quantity && formData.price_per_unit
    ? (parseInt(formData.quantity) * parseFloat(formData.price_per_unit)).toFixed(2)
    : '0.00';

  if (loading) {
    return <div className="p-8 text-center text-lg">Loading...</div>;
  }
  if (fetchError) {
    return <div className="p-8 text-center text-red-600">{fetchError}</div>;
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/purchases')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Drug Purchase</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Update drug purchase information</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Edit Purchase Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Drug Name *</label>
                  <select
                    name="drug_name"
                    value={formData.drug_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.drug_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  >
                    <option value="">Select a drug</option>
                    {allDrugsFromPurchases.map((drug, idx) => (
                      <option key={drug.id || idx} value={drug.drug_name}>
                        {drug.drug_name}
                      </option>
                    ))}
                  </select>
                  {errors.drug_name && <p className="text-red-500 text-sm mt-1">{errors.drug_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter quantity"
                  />
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Price per Unit ($) *</label>
                  <input
                    type="number"
                    name="price_per_unit"
                    value={formData.price_per_unit}
                    onChange={handleChange}
                    min="0"
                    step="0.01"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.price_per_unit ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter price per unit"
                  />
                  {errors.price_per_unit && <p className="text-red-500 text-sm mt-1">{errors.price_per_unit}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Total Amount ($)</label>
                  <input
                    type="text"
                    value={`$${totalAmount}`}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-100 dark:bg-gray-600 dark:text-white"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Supplier *</label>
                  <input
                    type="text"
                    name="supplier"
                    value={formData.supplier}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.supplier ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter supplier name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.supplier && <p className="text-red-500 text-sm mt-1">{errors.supplier}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Purchase Date *</label>
                  <input
                    type="date"
                    name="purchase_date"
                    value={formData.purchase_date}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.purchase_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                  />
                  {errors.purchase_date && <p className="text-red-500 text-sm mt-1">{errors.purchase_date}</p>}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}
              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Changes
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/purchases')}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {t('cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditDrugPurchasePage; 