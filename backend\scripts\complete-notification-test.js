import mysql from 'mysql2/promise';
import NotificationModel from '../models/notificationModel.js';
import ContactUsModel from '../models/contactUsModel.js';

async function completeNotificationTest() {
  let connection;
  
  try {
    console.log('🔍 Complete Notification System Test\n');

    // Create database connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'asmsaw'
    });

    console.log('✅ Database connected');

    // Test 1: Check table structure
    console.log('\n1. Checking Notifications table structure...');
    const [columns] = await connection.execute("DESCRIBE Notifications");
    console.log('Table columns:');
    columns.forEach(col => {
      console.log(`  - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? '(NOT NULL)' : '(NULL)'}`);
    });

    // Test 2: Create a test contact
    console.log('\n2. Creating a test contact...');
    const testContactData = {
      C_Title: 'Test Contact for Notifications',
      C_Body: 'This is a test contact to verify notifications work',
      user_name: 'Test User',
      user_email: '<EMAIL>',
      user_phone: '0701234567'
    };

    const contact = await ContactUsModel.create(testContactData);
    console.log('✅ Test contact created:', contact);

    // Test 3: Create notification for the contact
    console.log('\n3. Creating notification for the contact...');
    const notification = await NotificationModel.createContactNotification(contact);
    console.log('✅ Notification created:', notification);

    // Test 4: Verify notification in database
    console.log('\n4. Verifying notification in database...');
    const [dbNotifications] = await connection.execute(
      'SELECT * FROM Notifications WHERE contact_id = ? ORDER BY created_at DESC',
      [contact.C_Id]
    );
    console.log(`✅ Found ${dbNotifications.length} notifications in database for this contact`);
    
    if (dbNotifications.length > 0) {
      console.log('Database notification:', dbNotifications[0]);
    }

    // Test 5: Test NotificationModel methods
    console.log('\n5. Testing NotificationModel methods...');
    
    const allNotifications = await NotificationModel.getAll();
    console.log(`✅ getAll(): Found ${allNotifications.length} notifications`);
    
    const unreadCount = await NotificationModel.getUnreadCount();
    console.log(`✅ getUnreadCount(): ${unreadCount} unread notifications`);
    
    const contactNotifications = await NotificationModel.getByCategory('contacts');
    console.log(`✅ getByCategory('contacts'): Found ${contactNotifications.length} contact notifications`);

    // Test 6: Check recent contacts
    console.log('\n6. Checking recent contacts...');
    const recentContacts = await ContactUsModel.getAll();
    console.log(`✅ Found ${recentContacts.length} total contacts`);
    
    if (recentContacts.length > 0) {
      console.log('Recent contacts:');
      recentContacts.slice(0, 3).forEach((contact, index) => {
        console.log(`  ${index + 1}. ${contact.user_name} - ${contact.C_Title} (ID: ${contact.C_Id})`);
      });
    }

    // Test 7: Check if notifications exist for recent contacts
    console.log('\n7. Checking notifications for recent contacts...');
    const [notificationCount] = await connection.execute(
      'SELECT COUNT(*) as count FROM Notifications WHERE category = "contacts"'
    );
    console.log(`✅ Found ${notificationCount[0].count} contact notifications in database`);

    // Clean up test data
    console.log('\n8. Cleaning up test data...');
    await connection.execute('DELETE FROM Notifications WHERE contact_id = ?', [contact.C_Id]);
    await connection.execute('DELETE FROM ContactUs WHERE C_Id = ?', [contact.C_Id]);
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Total contacts: ${recentContacts.length}`);
    console.log(`   - Total notifications: ${allNotifications.length}`);
    console.log(`   - Unread notifications: ${unreadCount}`);
    console.log(`   - Contact notifications: ${contactNotifications.length}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error message:', error.message);
    console.error('SQL State:', error.sqlState);
    console.error('SQL Message:', error.sqlMessage);
  } finally {
    if (connection) {
      await connection.end();
      console.log('\n🔌 Database connection closed');
    }
  }
}

completeNotificationTest();
