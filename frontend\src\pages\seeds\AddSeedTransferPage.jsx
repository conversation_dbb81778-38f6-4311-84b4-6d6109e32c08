import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { useFarm } from '../../contexts/FarmContext';

const AddSeedTransferPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();
  const { farms } = useFarm();

  const [formData, setFormData] = useState({
    seed_id: '',
    seed_name: '',
    quantity: '',
    farm_id: '',
    farm_name: '',
    farm_location: '',
    transfer_date: '',
    transferred_by: '',
    notes: '',
    status: 'Pending',
  });

  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [purchases, setPurchases] = useState([]);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [selectedFarm, setSelectedFarm] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/seed-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // When a purchase is selected, update seed_id, seed_name, and available quantity
  const handlePurchaseChange = (e) => {
    const purchaseId = e.target.value;
    const purchase = purchases.find(p => String(p.id) === String(purchaseId));
    setSelectedPurchase(purchase);
    setFormData(prev => ({
      ...prev,
      seed_id: purchase ? purchase.id : '',
      seed_name: purchase ? purchase.seed_name : '',
      quantity: '', // reset quantity
    }));
    setErrors(prev => ({ ...prev, quantity: '' }));
  };

  // When a farm is selected, update farm details
  const handleFarmChange = (e) => {
    const farmId = e.target.value;
    const farm = farms.find(f => String(f.F_Id) === String(farmId));
    setSelectedFarm(farm);
    setFormData(prev => ({
      ...prev,
      farm_id: farm ? farm.F_Id : '',
      farm_name: farm ? farm.FName : '',
      farm_location: farm ? farm.FLocation : '',
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Calculate available quantity for the selected purchase
  const availableQuantity = selectedPurchase
    ? (selectedPurchase.remaining_quantity || (selectedPurchase.quantity - (selectedPurchase.transferred_quantity || 0)))
    : 0;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.seed_id) newErrors.seed_id = 'Please select a seed purchase';
    if (!formData.farm_id) newErrors.farm_id = 'Please select a farm';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (parseInt(formData.quantity) > availableQuantity) newErrors.quantity = `Cannot transfer more than available (${availableQuantity})`;
    if (!formData.transfer_date) newErrors.transfer_date = 'Transfer date is required';
    if (!formData.transferred_by.trim()) newErrors.transferred_by = 'Transferred by is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    setFeedback({ type: '', message: '' });
    if (!validateForm()) return;
    try {
      const transferData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        status: formData.status || 'Pending',
      };
      const response = await fetch('http://localhost:5432/api/v1/seed-transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save transfer');
      }
      setFeedback({ type: 'success', message: 'Transfer saved successfully!' });
      setTimeout(() => navigate('/admin/seeds/transfers'), 1500);
    } catch (error) {
      setFeedback({ type: 'error', message: error.message });
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/seeds/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Transfer Seeds</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Transfer seeds to farm
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Select Seed Purchase and Farm</CardTitle>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-4 p-3 rounded-md ${
                  feedback.type === 'success' ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'
                }`}
              >
                {feedback.message}
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Seed Purchase *</label>
                  <select
                    name="seed_id"
                    value={formData.seed_id}
                    onChange={handlePurchaseChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">Select a seed purchase...</option>
                    {purchases.map((purchase) => {
                      const remaining = purchase.remaining_quantity || (purchase.quantity - (purchase.transferred_quantity || 0));
                      return (
                        <option
                          key={purchase.id}
                          value={purchase.id}
                          disabled={remaining <= 0}
                        >
                          {purchase.seed_name} - {purchase.supplier} (Remaining: {remaining})
                          {remaining <= 0 ? ' - COMPLETED' : ''}
                        </option>
                      );
                    })}
                  </select>
                  {purchases.length === 0 && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>No seed purchases available.</strong> You need to create purchases first.<br />
                        <a href="/admin/seeds/purchases/add" className="text-yellow-800 underline hover:text-yellow-900">Click here to create a purchase</a>
                      </p>
                    </div>
                  )}
                  {selectedPurchase && (
                    <div className={`mt-2 p-3 rounded-md ${availableQuantity > 0 ? 'bg-blue-50' : 'bg-red-50'}`}>
                      <div className={`text-sm ${availableQuantity > 0 ? 'text-blue-700' : 'text-red-700'}`}>
                        <div><strong>Seed:</strong> {selectedPurchase.seed_name}</div>
                        <div><strong>Supplier:</strong> {selectedPurchase.supplier}</div>
                        <div><strong>Total Quantity:</strong> {selectedPurchase.quantity}</div>
                        <div><strong>Transferred:</strong> {selectedPurchase.transferred_quantity || 0}</div>
                        <div><strong>Remaining:</strong>
                          <span className={`ml-1 font-bold ${availableQuantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {availableQuantity}
                          </span>
                          {availableQuantity === 0 && (
                            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                              COMPLETED
                            </span>
                          )}
                        </div>
                        <div><strong>Purchase Date:</strong> {selectedPurchase.purchase_date ? new Date(selectedPurchase.purchase_date).toLocaleDateString() : ''}</div>
                      </div>
                    </div>
                  )}
                  {errors.seed_id && <p className="text-red-500 text-sm mt-1">{errors.seed_id}</p>}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm *</label>
                  <select
                    name="farm_id"
                    value={formData.farm_id}
                    onChange={handleFarmChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">Select a farm...</option>
                    {farms.map((farm) => (
                      <option key={farm.F_Id} value={farm.F_Id}>
                        {farm.FName} - {farm.FLocation}
                      </option>
                    ))}
                  </select>
                  {selectedFarm && (
                    <div className="mt-2 p-3 bg-green-50 rounded-md">
                      <div className="text-sm text-green-700">
                        <div><strong>Farm:</strong> {selectedFarm.FName}</div>
                        <div><strong>Location:</strong> {selectedFarm.FLocation}</div>
                      </div>
                    </div>
                  )}
                  {errors.farm_id && <p className="text-red-500 text-sm mt-1">{errors.farm_id}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    max={availableQuantity}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder="Enter quantity to transfer"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {availableQuantity > 0 && (
                    <small className="text-gray-500 mt-1 block">
                      Maximum available: {availableQuantity}
                    </small>
                  )}
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transfer Date *</label>
                  <input
                    type="date"
                    name="transfer_date"
                    value={formData.transfer_date}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.transfer_date && <p className="text-red-500 text-sm mt-1">{errors.transfer_date}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transferred By *</label>
                  <input
                    type="text"
                    name="transferred_by"
                    value={formData.transferred_by}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder="Enter person name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.transferred_by && <p className="text-red-500 text-sm mt-1">{errors.transferred_by}</p>}
                </div>


              </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}

              <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/seeds/transfers')}
                >
                  {t('cancel') || 'Cancel'}
                </Button>
                <Button
                  type="submit"
                  disabled={availableQuantity <= 0}
                  className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''} ${
                    availableQuantity <= 0 ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                >
                  <Save className="h-4 w-4" />
                  {availableQuantity <= 0 ? 'Transfer Completed' : 'Save Transfer'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddSeedTransferPage;
