import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';

const AddSeedTransferPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();

  const [formData, setFormData] = useState({
    seed_id: '',
    seed_name: '',
    quantity: '',
    farm_id: '',
    farm_name: '',
    farm_location: '',
    transfer_date: '',
    transferred_by: '',
    notes: '',
    status: 'Pending',
  });

  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [purchases, setPurchases] = useState([]);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [selectedFarm, setSelectedFarm] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/seed-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // When a purchase is selected, update seed_id, seed_name, and available quantity
  const handlePurchaseChange = (e) => {
    const purchaseId = e.target.value;
    const purchase = purchases.find(p => String(p.id) === String(purchaseId));
    setSelectedPurchase(purchase);
    setFormData(prev => ({
      ...prev,
      seed_id: purchase ? purchase.id : '',
      seed_name: purchase ? purchase.seed_name : '',
      quantity: '', // reset quantity
    }));
    setErrors(prev => ({ ...prev, quantity: '' }));
  };

  // When a farm is selected, update farm details
  const handleFarmChange = (e) => {
    const farmId = e.target.value;
    const farm = farms.find(f => String(f.F_Id) === String(farmId));
    setSelectedFarm(farm);
    setFormData(prev => ({
      ...prev,
      farm_id: farm ? farm.F_Id : '',
      farm_name: farm ? farm.FName : '',
      farm_location: farm ? farm.FLocation : '',
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Calculate available quantity for the selected purchase
  const availableQuantity = selectedPurchase
    ? (selectedPurchase.remaining_quantity || (selectedPurchase.quantity - (selectedPurchase.transferred_quantity || 0)))
    : 0;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.seed_id) newErrors.seed_id = 'Please select a seed purchase';
    if (!formData.farm_id) newErrors.farm_id = 'Please select a farm';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (parseInt(formData.quantity) > availableQuantity) newErrors.quantity = `Cannot transfer more than available (${availableQuantity})`;
    if (!formData.transfer_date) newErrors.transfer_date = 'Transfer date is required';
    if (!formData.transferred_by.trim()) newErrors.transferred_by = 'Transferred by is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    setFeedback({ type: '', message: '' });
    if (!validateForm()) return;
    try {
      const transferData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        status: formData.status || 'Pending',
      };
      const response = await fetch('http://localhost:5432/api/v1/seed-transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save transfer');
      }
      setFeedback({ type: 'success', message: 'Transfer saved successfully!' });
      setTimeout(() => navigate('/admin/seeds/transfers'), 1500);
    } catch (error) {
      setFeedback({ type: 'error', message: error.message });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/seeds/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Transfer Seeds</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Transfer seeds to farm
            </p>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Transfer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Seed Name *
                  </label>
                  <input
                    type="text"
                    name="seed_name"
                    value={formData.seed_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.seed_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter seed name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.seed_name && (
                    <p className="text-red-500 text-sm mt-1">{errors.seed_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quantity *
                  </label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter quantity"
                  />
                  {errors.quantity && (
                    <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Farm Name *
                  </label>
                  <input
                    type="text"
                    name="farm_name"
                    value={formData.farm_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.farm_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter farm name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.farm_name && (
                    <p className="text-red-500 text-sm mt-1">{errors.farm_name}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Farm Location
                  </label>
                  <input
                    type="text"
                    name="farm_location"
                    value={formData.farm_location}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                    placeholder="Enter farm location"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Transfer Date *
                  </label>
                  <input
                    type="date"
                    name="transfer_date"
                    value={formData.transfer_date}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.transfer_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                  />
                  {errors.transfer_date && (
                    <p className="text-red-500 text-sm mt-1">{errors.transfer_date}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Transferred By *
                  </label>
                  <input
                    type="text"
                    name="transferred_by"
                    value={formData.transferred_by}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${
                      errors.transferred_by ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'
                    }`}
                    placeholder="Enter person name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.transferred_by && (
                    <p className="text-red-500 text-sm mt-1">{errors.transferred_by}</p>
                  )}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Notes
                </label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>

              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}

              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button
                  type="submit"
                  className="flex items-center gap-2"
                >
                  <Save className="h-4 w-4" />
                  Save Transfer
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/seeds/transfers')}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {t('cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddSeedTransferPage;
