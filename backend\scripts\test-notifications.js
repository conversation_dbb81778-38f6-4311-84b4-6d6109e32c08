import axios from 'axios';

async function testNotificationSystem() {
  try {
    console.log('🧪 Testing Notification System...\n');

    // Test 1: Submit a contact form
    console.log('1. Testing contact form submission...');
    const contactData = {
      C_Title: 'Test Contact Form',
      C_Body: 'This is a test message to verify notifications work properly.',
      user_name: 'Test User',
      user_email: '<EMAIL>',
      user_phone: '0701234567'
    };

    const contactResponse = await axios.post('http://localhost:5432/api/v1/contact-us', contactData);
    console.log('✅ Contact form submitted successfully');
    console.log('Contact ID:', contactResponse.data.data.C_Id);

    // Wait a moment for notification to be created
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Test 2: Check if notification was created (requires admin token)
    console.log('\n2. Testing notification retrieval...');
    console.log('ℹ️  Note: You need to be logged in as admin to test notification endpoints');
    console.log('ℹ️  Visit http://localhost:3000/admin/notifications/contacts to see the notification');

    console.log('\n✅ Test completed! Check the admin panel for notifications.');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

testNotificationSystem();
