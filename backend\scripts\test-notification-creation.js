import NotificationModel from '../models/notificationModel.js';

async function testNotificationCreation() {
  try {
    console.log('🧪 Testing notification creation...\n');

    // Test 1: Create a simple notification
    console.log('1. Creating a test notification...');
    const testNotification = await NotificationModel.create({
      type: 'test',
      title: 'Test Notification',
      message: 'This is a test notification',
      category: 'system',
      data: { test: true }
    });
    console.log('✅ Test notification created:', testNotification);

    // Test 2: Create a contact notification
    console.log('\n2. Creating a contact notification...');
    const mockContact = {
      C_Id: 999,
      user_name: 'Test User',
      user_email: '<EMAIL>',
      C_Title: 'Test Contact',
      C_Body: 'Test message',
      user_phone: '0701234567'
    };

    const contactNotification = await NotificationModel.createContactNotification(mockContact);
    console.log('✅ Contact notification created:', contactNotification);

    // Test 3: Fetch all notifications
    console.log('\n3. Fetching all notifications...');
    const allNotifications = await NotificationModel.getAll();
    console.log(`✅ Found ${allNotifications.length} notifications`);
    
    if (allNotifications.length > 0) {
      console.log('Recent notifications:');
      allNotifications.slice(0, 3).forEach((notif, index) => {
        console.log(`  ${index + 1}. ${notif.title} - ${notif.message}`);
      });
    }

    // Test 4: Get unread count
    console.log('\n4. Getting unread count...');
    const unreadCount = await NotificationModel.getUnreadCount();
    console.log(`✅ Unread notifications: ${unreadCount}`);

    console.log('\n✅ All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

testNotificationCreation();
