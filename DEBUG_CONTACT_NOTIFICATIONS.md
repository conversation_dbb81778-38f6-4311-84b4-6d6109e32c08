# 🔍 Debug Contact Notifications Not Showing

## Issue: Contact notifications not appearing in main notifications page

Let's debug this step by step to find where the issue is.

## 🧪 Step 1: Test Backend Notification Creation

Run this test to verify notifications are being created:

```bash
cd backend
node scripts/test-contact-notification-flow.js
```

**Expected output:**
```
✅ Contact created: { C_Id: 123, ... }
✅ Notification created: { id: 456, ... }
✅ Total notifications in database: X
✅ Contact notifications: X
```

## 🧪 Step 2: Test Real Contact Form Submission

1. **Submit a contact form** at `http://localhost:3000/contact`
2. **Check backend console** for these messages:
   ```
   ✅ Contact created successfully: { C_Id: 123, ... }
   ✅ Notification created successfully: { id: 456, ... }
   ```

**If you DON'T see these messages:**
- The notification creation is failing
- Check if NotificationModel is properly imported
- Check database connection

## 🧪 Step 3: Test API Endpoints Directly

**Login as admin** and run these in browser console:

```javascript
// Test 1: Check if you have auth token
const token = localStorage.getItem('token');
console.log('Auth token:', token ? 'Found' : 'Missing');

// Test 2: Create a test notification
fetch('http://localhost:5432/api/v1/notifications/test-contact', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  }
})
.then(r => r.json())
.then(data => {
  console.log('✅ Test notification created:', data);
  
  // Test 3: Fetch all notifications
  return fetch('http://localhost:5432/api/v1/notifications', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
})
.then(r => r.json())
.then(data => {
  console.log('✅ All notifications from API:', data);
  console.log('✅ Contact notifications:', data.data.filter(n => n.category === 'contacts'));
})
.catch(err => console.error('❌ Error:', err));
```

## 🧪 Step 4: Check Frontend Console

1. **Go to** `http://localhost:3000/admin/notifications`
2. **Open browser console** (F12 → Console)
3. **Look for these messages:**
   ```
   📡 Fetching notifications from backend...
   📡 Notifications API response: { success: true, data: [...] }
   ✅ Processed notifications: [...]
   ✅ Contact notifications: [...]
   ✅ Contact notifications count: X
   📋 Current notifications: [...]
   📋 Contact notifications: [...]
   ```

## 🧪 Step 5: Manual Database Check

If you have database access, run these queries:

```sql
-- Check if notifications table exists
SHOW TABLES LIKE 'Notifications';

-- Check all notifications
SELECT * FROM Notifications ORDER BY created_at DESC LIMIT 10;

-- Check contact notifications specifically
SELECT * FROM Notifications WHERE category = 'contacts' ORDER BY created_at DESC;

-- Check recent contacts
SELECT * FROM ContactUs ORDER BY C_Id DESC LIMIT 5;
```

## 🔧 Common Issues and Solutions

### Issue 1: Backend not creating notifications
**Symptoms:** No success messages in backend console
**Solutions:**
- Check if NotificationModel is imported in contactUsController.js
- Verify database connection
- Check if Notifications table exists

### Issue 2: Notifications created but not fetched
**Symptoms:** Backend shows success, frontend shows empty
**Solutions:**
- Check auth token in browser
- Verify API endpoints are accessible
- Check CORS settings

### Issue 3: Frontend not processing notifications
**Symptoms:** API returns data but UI shows empty
**Solutions:**
- Check browser console for errors
- Verify notification context is working
- Check if notifications are being filtered correctly

### Issue 4: Database/Table issues
**Symptoms:** Database errors in backend
**Solutions:**
- Run table creation script: `node scripts/create-notifications-table.js`
- Check database name is 'asmsaw'
- Verify database connection settings

## 🚨 Quick Fixes to Try

### Fix 1: Force refresh notifications
```javascript
// In browser console on admin page
window.location.reload();
```

### Fix 2: Clear localStorage and re-login
```javascript
localStorage.clear();
// Then login again
```

### Fix 3: Test with manual notification creation
```javascript
// Create a test notification via API
fetch('http://localhost:5432/api/v1/notifications', {
  method: 'POST',
  headers: { 
    'Authorization': `Bearer ${localStorage.getItem('token')}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    type: 'new_contact',
    title: 'Test Contact Notification',
    message: 'This is a test contact notification',
    category: 'contacts',
    data: {
      contactId: 999,
      userName: 'Test User',
      userEmail: '<EMAIL>'
    }
  })
})
.then(r => r.json())
.then(data => {
  console.log('Manual notification created:', data);
  window.location.reload();
});
```

## 📋 Debugging Checklist

- [ ] Backend shows contact creation success
- [ ] Backend shows notification creation success
- [ ] API test creates notification successfully
- [ ] API returns notifications when fetched
- [ ] Frontend console shows notification fetch success
- [ ] Frontend console shows contact notifications
- [ ] Auth token exists and is valid
- [ ] Database has Notifications table
- [ ] Database has contact notifications

## 🎯 Next Steps

Based on what you find:

1. **If backend fails:** Check database and model imports
2. **If API fails:** Check authentication and routes
3. **If frontend fails:** Check notification context and processing
4. **If all works but UI empty:** Check notification filtering and display logic

Please run these tests and share the results so I can help you fix the specific issue!
