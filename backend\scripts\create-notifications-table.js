import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function createNotificationsTable() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'asmsaw'
    });
    
    console.log('Connected to database...');
    
    // Read SQL file
    const sqlPath = path.join(__dirname, '../database/notifications_table.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute SQL
    await connection.execute(sql);
    console.log('✅ Notifications table created successfully!');
    
  } catch (error) {
    console.error('❌ Error creating notifications table:', error.message);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed.');
    }
  }
}

createNotificationsTable();
