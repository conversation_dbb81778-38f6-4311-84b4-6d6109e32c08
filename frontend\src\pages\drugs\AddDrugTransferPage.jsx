import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { useFarm } from '../../contexts/FarmContext';

const AddDrugTransferPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();
  const { farms } = useFarm();

  const [formData, setFormData] = useState({
    drug_id: '',
    drug_name: '',
    quantity: '',
    farm_id: '',
    farm_name: '',
    farm_location: '',
    transfer_date: '',
    transferred_by: '',
    notes: '',
    status: 'Pending',
  });
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [purchases, setPurchases] = useState([]);
  const [selectedPurchase, setSelectedPurchase] = useState(null);
  const [selectedFarm, setSelectedFarm] = useState(null);
  const [feedback, setFeedback] = useState({ type: '', message: '' });

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-purchases')
      .then(res => res.json())
      .then(json => setPurchases(json.data || []));
  }, []);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // When a purchase is selected, update drug_id, drug_name, and available quantity
  const handlePurchaseChange = (e) => {
    const purchaseId = e.target.value;
    const purchase = purchases.find(p => String(p.id) === String(purchaseId));
    setSelectedPurchase(purchase);
    setFormData(prev => ({
      ...prev,
      drug_id: purchase ? purchase.id : '',
      drug_name: purchase ? purchase.drug_name : '',
      quantity: '', // reset quantity
    }));
    setErrors(prev => ({ ...prev, quantity: '' }));
  };

  // When a farm is selected, update farm_id, farm_name, and farm_location
  const handleFarmChange = (e) => {
    const farmId = e.target.value;
    const farm = farms.find(f => String(f.id) === String(farmId));
    setSelectedFarm(farm);
    setFormData(prev => ({
      ...prev,
      farm_id: farm ? farm.id : '',
      farm_name: farm ? farm.name : '',
      farm_location: farm ? farm.location : '',
    }));
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  // Calculate available quantity for the selected purchase
  const availableQuantity = selectedPurchase
    ? (selectedPurchase.quantity - (selectedPurchase.transferred_quantity || 0))
    : 0;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.drug_id) newErrors.drug_id = 'Please select a drug purchase';
    if (!formData.farm_id) newErrors.farm_id = 'Please select a farm';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (parseInt(formData.quantity) > availableQuantity) newErrors.quantity = `Cannot transfer more than available (${availableQuantity})`;
    if (!formData.transfer_date) newErrors.transfer_date = 'Transfer date is required';
    if (!formData.transferred_by.trim()) newErrors.transferred_by = 'Transferred by is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    setFeedback({ type: '', message: '' });
    if (!validateForm()) return;
    try {
      const transferData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        status: formData.status || 'Pending',
      };
      const response = await fetch('http://localhost:5432/api/v1/drug-transfers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save transfer');
      }
      await response.json();
      setFeedback({ type: 'success', message: 'Transfer saved successfully!' });
      setTimeout(() => {
      navigate('/admin/drugs/transfers');
      }, 1200);
    } catch (error) {
      setFeedback({ type: 'error', message: error.message || 'Failed to add transfer. Please try again.' });
    }
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Transfer Drugs</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Transfer drugs to farm</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Select Drug Purchase and Farm</CardTitle>
          </CardHeader>
          <CardContent>
            {feedback.message && (
              <div
                className={`mb-6 p-4 rounded-lg ${
                  feedback.type === 'success' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`}
              >
                {feedback.message}
              </div>
            )}
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Drug Purchase *</label>
                  <select
                    name="drug_id"
                    value={formData.drug_id}
                    onChange={handlePurchaseChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">Select a drug purchase...</option>
                    {purchases.map((purchase) => (
                      <option key={purchase.id} value={purchase.id}>
                        {purchase.drug_name} - {purchase.supplier} (Available: {purchase.quantity - (purchase.transferred_quantity || 0)})
                      </option>
                    ))}
                  </select>
                  {purchases.length === 0 && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>No drug purchases available.</strong> You need to create purchases first.<br />
                        <a href="/admin/drugs/purchases/add" className="text-yellow-800 underline hover:text-yellow-900">Click here to create a purchase</a>
                      </p>
                    </div>
                  )}
                  {selectedPurchase && (
                    <div className="mt-2 p-3 bg-blue-50 rounded-md">
                      <div className="text-sm text-blue-700">
                        <div><strong>Drug:</strong> {selectedPurchase.drug_name}</div>
                        <div><strong>Supplier:</strong> {selectedPurchase.supplier}</div>
                        <div><strong>Available:</strong> {selectedPurchase.quantity - (selectedPurchase.transferred_quantity || 0)}</div>
                        <div><strong>Purchase Date:</strong> {selectedPurchase.purchase_date ? new Date(selectedPurchase.purchase_date).toLocaleDateString() : ''}</div>
                      </div>
                    </div>
                  )}
                  {errors.drug_id && <p className="text-red-500 text-sm mt-1">{errors.drug_id}</p>}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm *</label>
                  <select
                    name="farm_id"
                    value={formData.farm_id}
                    onChange={handleFarmChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  >
                    <option value="">Select a farm...</option>
                    {farms && farms.length > 0 ? farms.map((farm) => (
                      <option key={farm.id} value={farm.id}>
                        {farm.name} - {farm.owner}
                      </option>
                    )) : (
                      <option value="" disabled>No farms available</option>
                    )}
                  </select>
                  {(!farms || farms.length === 0) && (
                    <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-700">
                        <strong>No farms available.</strong> You need to create farms first.<br />
                        <a href="/admin/farms/add" className="text-yellow-800 underline hover:text-yellow-900">Click here to create a farm</a>
                      </p>
                    </div>
                  )}
                  {selectedFarm && (
                    <div className="mt-2 p-3 bg-green-50 rounded-md">
                      <div className="text-sm text-green-700">
                        <div><strong>Farm:</strong> {selectedFarm.name}</div>
                        <div><strong>Owner:</strong> {selectedFarm.owner}</div>
                        <div><strong>Email:</strong> {selectedFarm.email}</div>
                        <div><strong>Phone:</strong> {selectedFarm.phone}</div>
                      </div>
                    </div>
                  )}
                  {errors.farm_id && <p className="text-red-500 text-sm mt-1">{errors.farm_id}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transfer Date *</label>
                  <input
                    type="date"
                    name="transfer_date"
                    value={formData.transfer_date}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  />
                  {errors.transfer_date && <p className="text-red-500 text-sm mt-1">{errors.transfer_date}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    required
                    min="1"
                    max={availableQuantity}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder={availableQuantity ? `Max: ${availableQuantity}` : 'Enter quantity'}
                    disabled={!selectedPurchase}
                  />
                  {selectedPurchase && (
                    <small className="text-gray-500">
                      Maximum available: {availableQuantity}
                    </small>
                  )}
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transferred By *</label>
                  <input
                    type="text"
                    name="transferred_by"
                    value={formData.transferred_by}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                    placeholder="Enter person name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.transferred_by && <p className="text-red-500 text-sm mt-1">{errors.transferred_by}</p>}
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white dark:border-gray-600"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
                </div>
              <div className={`flex justify-end gap-2 pt-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/transfers')}
                >
                  {t('cancel') || 'Cancel'}
                </Button>
                <Button
                  type="submit"
                  className={`flex items-center gap-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                >
                  <Save className="h-4 w-4" />
                  Save Transfer
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AddDrugTransferPage;
