import async<PERSON>and<PERSON> from "../middlewares/asyncHandler.js";
import ContactReadStatusModel from "../models/contactReadStatusModel.js";

const ContactReadStatusController = {
  // Mark a contact as read
  markAsRead: asyncHandler(async (req, res) => {
    const { contactId } = req.params;
    const userId = req.user.u_Id; // Get user ID from authenticated user

    await ContactReadStatusModel.markAsRead(contactId, userId);

    res.json({
      success: true,
      message: "Contact marked as read",
    });
  }),

  // Mark all contacts as read
  markAllAsRead: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id; // Get user ID from authenticated user

    const result = await ContactReadStatusModel.markAllAsRead(userId);

    res.json({
      success: true,
      message: "All contacts marked as read",
      affectedRows: result.affectedRows,
    });
  }),

  // Get read status for all contacts
  getReadStatus: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id; // Get user ID from authenticated user

    const readContactIds = await ContactReadStatusModel.getReadStatusForUser(userId);

    res.json({
      success: true,
      data: readContactIds,
    });
  }),

  // Get contacts with read status
  getContactsWithReadStatus: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id; // Get user ID from authenticated user

    const contacts = await ContactReadStatusModel.getContactsWithReadStatus(userId);

    res.json({
      success: true,
      total: contacts.length,
      data: contacts,
    });
  }),

  // Clear all read status
  clearAllReadStatus: asyncHandler(async (req, res) => {
    const userId = req.user.u_Id; // Get user ID from authenticated user

    const result = await ContactReadStatusModel.clearAllReadStatus(userId);

    res.json({
      success: true,
      message: "All read status cleared",
      affectedRows: result.affectedRows,
    });
  }),
};

export default ContactReadStatusController;
