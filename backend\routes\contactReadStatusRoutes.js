import express from "express";
import ContactReadStatusController from "../controllers/contactReadStatusController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

// All routes require admin authentication
router.use(authenticate);
router.use(authorizeAdmin);

// Get read status for all contacts
router.get("/", ContactReadStatusController.getReadStatus);

// Get contacts with read status
router.get("/contacts", ContactReadStatusController.getContactsWithReadStatus);

// Mark a specific contact as read
router.post("/:contactId/read", ContactReadStatusController.markAsRead);

// Mark all contacts as read
router.post("/mark-all-read", ContactReadStatusController.markAllAsRead);

// Clear all read status
router.delete("/clear-all", ContactReadStatusController.clearAllReadStatus);

export default router;
