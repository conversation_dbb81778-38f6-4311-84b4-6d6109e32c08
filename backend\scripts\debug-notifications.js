import mysql from 'mysql2/promise';

async function debugNotifications() {
  let connection;
  
  try {
    // Create connection
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'root',
      password: '',
      database: 'asmsaw'
    });
    
    console.log('🔍 Debugging Notifications System...\n');
    
    // Check if notifications table exists
    console.log('1. Checking if Notifications table exists...');
    const [tables] = await connection.execute("SHOW TABLES LIKE 'Notifications'");
    if (tables.length === 0) {
      console.log('❌ Notifications table does not exist!');
      return;
    }
    console.log('✅ Notifications table exists');
    
    // Check table structure
    console.log('\n2. Checking table structure...');
    const [columns] = await connection.execute("DESCRIBE Notifications");
    console.log('Table columns:', columns.map(col => col.Field).join(', '));
    
    // Check if there are any notifications
    console.log('\n3. Checking existing notifications...');
    const [notifications] = await connection.execute("SELECT * FROM Notifications ORDER BY created_at DESC LIMIT 5");
    console.log(`Found ${notifications.length} notifications`);
    
    if (notifications.length > 0) {
      console.log('Recent notifications:');
      notifications.forEach((notif, index) => {
        console.log(`  ${index + 1}. ${notif.title} - ${notif.message} (${notif.created_at})`);
      });
    }
    
    // Check recent contact submissions
    console.log('\n4. Checking recent contact submissions...');
    const [contacts] = await connection.execute("SELECT * FROM ContactUs ORDER BY C_Id DESC LIMIT 3");
    console.log(`Found ${contacts.length} recent contacts`);
    
    if (contacts.length > 0) {
      console.log('Recent contacts:');
      contacts.forEach((contact, index) => {
        console.log(`  ${index + 1}. ${contact.user_name} - ${contact.C_Title} (ID: ${contact.C_Id})`);
      });
    }
    
    // Check if there are notifications for recent contacts
    if (contacts.length > 0) {
      console.log('\n5. Checking notifications for recent contacts...');
      const contactIds = contacts.map(c => c.C_Id);
      const [contactNotifications] = await connection.execute(
        `SELECT * FROM Notifications WHERE contact_id IN (${contactIds.join(',')}) ORDER BY created_at DESC`
      );
      console.log(`Found ${contactNotifications.length} notifications for recent contacts`);
      
      if (contactNotifications.length > 0) {
        contactNotifications.forEach((notif, index) => {
          console.log(`  ${index + 1}. Contact ID ${notif.contact_id}: ${notif.message}`);
        });
      } else {
        console.log('⚠️  No notifications found for recent contacts - this might be the issue!');
      }
    }
    
    console.log('\n✅ Debug completed!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

debugNotifications();
