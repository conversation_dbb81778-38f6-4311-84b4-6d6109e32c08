import db from "../config/db.js";

const DrugPurchasesModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Drug_Purchases (drug_id, drug_name, quantity, price_per_unit, total_amount, supplier, purchase_date, status, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.drug_id || null,
        data.drug_name,
        data.quantity,
        data.price_per_unit,
        data.total_amount,
        data.supplier,
        data.purchase_date,
        data.status || 'Pending',
        data.notes || null,
      ],
    );

    const [newPurchase] = await db.query("SELECT * FROM Drug_Purchases WHERE id = ?", [
      result.insertId,
    ]);
    return newPurchase[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM Drug_Purchases ORDER BY purchase_date DESC");
    return rows;
  },

  // Get all drug purchases with remaining quantities
  getAllWithRemainingQuantity: async () => {
    const [rows] = await db.execute(`
      SELECT
        dp.*,
        COALESCE(SUM(dt.quantity), 0) as transferred_quantity,
        (dp.quantity - COALESCE(SUM(dt.quantity), 0)) as remaining_quantity
      FROM Drug_Purchases dp
      LEFT JOIN Drug_Transfers dt ON dp.id = dt.drug_id AND dt.status IN ('Completed', 'Pending', 'In Transit')
      GROUP BY dp.id
      ORDER BY dp.purchase_date DESC
    `);
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM Drug_Purchases WHERE id = ?", [id]);
    return rows[0];
  },

  getByDrugId: async (drugId) => {
    const [rows] = await db.execute("SELECT * FROM Drug_Purchases WHERE drug_id = ? ORDER BY purchase_date DESC", [drugId]);
    return rows;
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Drug_Purchases SET drug_id = ?, drug_name = ?, quantity = ?, price_per_unit = ?, total_amount = ?, supplier = ?, purchase_date = ?, status = ?, notes = ?
       WHERE id = ?`,
      [
        data.drug_id,
        data.drug_name,
        data.quantity,
        data.price_per_unit,
        data.total_amount,
        data.supplier,
        data.purchase_date,
        data.status,
        data.notes,
        id,
      ],
    );
    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Drug_Purchases WHERE id = ?", [id]);
    return result;
  },

  // Get total quantity purchased for a specific drug
  getTotalPurchasedQuantity: async (drugId) => {
    const [rows] = await db.execute(
      "SELECT SUM(quantity) as total_quantity FROM Drug_Purchases WHERE drug_id = ? AND status = 'Completed'",
      [drugId]
    );
    return rows[0]?.total_quantity || 0;
  },

  // Get purchase statistics
  getStatistics: async () => {
    const [rows] = await db.execute(`
      SELECT 
        COUNT(*) as total_purchases,
        SUM(quantity) as total_quantity,
        SUM(total_amount) as total_amount,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_purchases,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_purchases
      FROM Drug_Purchases
    `);
    return rows[0];
  },
};

export default DrugPurchasesModel;
