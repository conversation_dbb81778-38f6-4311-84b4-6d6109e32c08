import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLanguage } from './LanguageContext';
import axios from 'axios';
// import { webSocketService } from '../services/websocket';

const NotificationContext = createContext();

export const NotificationProvider = ({ children }) => {
  const { language, translations } = useLanguage();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [notificationSettings, setNotificationSettings] = useState({
    email: true,
    browser: true,
    sound: true,
    categories: {
      system: true,
      inventory: true,
      orders: true,
      users: true,
      reports: true,
      contacts: true,
    },
  });

  // Load notifications and settings from localStorage on initial render
  useEffect(() => {
    const savedSettings = localStorage.getItem('notificationSettings');

    if (savedSettings) {
      setNotificationSettings(JSON.parse(savedSettings));
    }

    // Fetch notifications from backend instead of localStorage
    fetchNotifications();
    fetchUnreadCount();

    // Set up polling for new notifications every 30 seconds
    const interval = setInterval(() => {
      fetchNotifications();
      fetchUnreadCount();
    }, 30000);

    // Set up WebSocket notification callback
    // webSocketService.setNotificationCallback((type, message, data) => {
    //   addNotification(type, message, data);
    // });

    // Connect to WebSocket
    // webSocketService.connect();

    return () => {
      clearInterval(interval);
      // webSocketService.disconnect();
    };
  }, []);

  // Save notifications and settings to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem('notifications', JSON.stringify(notifications));
    setUnreadCount(notifications.filter((n) => !n.read).length);
  }, [notifications]);

  useEffect(() => {
    localStorage.setItem('notificationSettings', JSON.stringify(notificationSettings));
  }, [notificationSettings]);

  // Get translation function with safe fallback
  const t = (key, variables = {}) => {
  if (!translations || !translations[language]) return key;
  let text = translations[language][key] || key;

  // Replace variables like {orderId}, {customer} in the text
  Object.entries(variables).forEach(([varName, value]) => {
    const regex = new RegExp(`{${varName}}`, 'g');
    text = text.replace(regex, value);
  });

  return text;
};


  const addNotification = (type, message, data = {}) => {
    const newNotification = {
      id: Date.now(),
      type,
      category: data.category || 'system',
      message,
      data,
      timestamp: new Date().toISOString(),
      read: false,
      archived: false,
    };

    // Check if notification category is enabled
    if (!notificationSettings.categories[newNotification.category]) {
      return;
    }

    setNotifications((prev) => [newNotification, ...prev]);

    // Show browser notification if enabled and supported
    if (notificationSettings.browser && 'Notification' in window && Notification.permission === 'granted') {
      new Notification(t('notifications'), {
        body: message,
        icon: '/logo.png',
      });
    }

    // Play sound if enabled
    if (notificationSettings.sound) {
      const audio = new Audio('/notification.mp3');
      audio.play().catch((e) => console.error('Error playing notification sound:', e));
    }

    // Send email notification if enabled
    if (notificationSettings.email && data.email) {
      // Implement email notification logic here
      console.log('Sending email notification to:', data.email);
    }
  };

  const markAsRead = async (id) => {
    setNotifications((prev) =>
      prev.map((notification) => (notification.id === id ? { ...notification, read: true } : notification))
    );
    await markAsReadBackend(id);
    await fetchUnreadCount();
  };

  const markAllAsRead = () => {
    setNotifications((prev) => prev.map((notification) => ({ ...notification, read: true })));
  };

  const clearNotification = async (id) => {
    setNotifications((prev) => prev.filter((notification) => notification.id !== id));
    await deleteNotificationBackend(id);
    await fetchUnreadCount();
  };

  const archiveNotification = (id) => {
    setNotifications((prev) =>
      prev.map((notification) => (notification.id === id ? { ...notification, archived: true } : notification))
    );
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const updateNotificationSettings = (newSettings) => {
    setNotificationSettings((prev) => ({
      ...prev,
      ...newSettings,
    }));
  };

  // Specific notification types
  const notifyLowStock = (itemName, quantity) => {
    addNotification('low_stock', t('low_stock_alert', { item: itemName, quantity }), {
      category: 'inventory',
      itemName,
      quantity,
    });
  };

  const notifyExpiry = (itemName, daysUntilExpiry) => {
    addNotification('expiry_alert', t('expiry_alert', { item: itemName, days: daysUntilExpiry }), {
      category: 'inventory',
      itemName,
      daysUntilExpiry,
    });
  };

  const notifyNewOrder = (orderId, customerName) => {
    addNotification('new_order', t('new_order_alert', { orderId, customer: customerName }), {
      category: 'orders',
      orderId,
      customerName,
    });
  };

  const notifyNewUser = (userId, userName) => {
    addNotification('new_user', t('new_user_alert', { userId, user: userName }), {
      category: 'users',
      userId,
      userName,
    });
  };

  const notifyReportGenerated = (reportId, reportName) => {
    addNotification('report_generated', t('report_generated_alert', { reportId, report: reportName }), {
      category: 'reports',
      reportId,
      reportName,
    });
  };

  const notifyNewContact = (contactData) => {
    addNotification('new_contact', `New contact from ${contactData.user_name}: ${contactData.subject}`, {
      category: 'contacts',
      contactId: contactData.contact_id,
      userName: contactData.user_name,
      userEmail: contactData.user_email,
      subject: contactData.subject,
      message: contactData.message,
      phone: contactData.phone,
    });
  };

  // Request notification permission
  const requestNotificationPermission = () => {
    if ('Notification' in window) {
      Notification.requestPermission();
    }
  };

  // Fetch notifications from backend
  const fetchNotifications = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get('http://localhost:5432/api/v1/notifications', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        const backendNotifications = response.data.data.map(notification => ({
          id: notification.id,
          type: notification.type,
          category: notification.category,
          message: notification.message,
          title: notification.title,
          data: JSON.parse(notification.data || '{}'),
          timestamp: notification.created_at,
          read: notification.read_status,
          archived: false,
        }));
        setNotifications(backendNotifications);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    }
  };

  // Fetch unread count
  const fetchUnreadCount = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get('http://localhost:5432/api/v1/notifications/unread-count', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setUnreadCount(response.data.unreadCount);
      }
    } catch (error) {
      console.error('Error fetching unread count:', error);
    }
  };

  // Mark notification as read on backend
  const markAsReadBackend = async (id) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      await axios.patch(`http://localhost:5432/api/v1/notifications/${id}/read`, {}, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Delete notification on backend
  const deleteNotificationBackend = async (id) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      await axios.delete(`http://localhost:5432/api/v1/notifications/${id}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        unreadCount,
        notificationSettings,
        addNotification,
        markAsRead,
        markAllAsRead,
        clearNotification,
        archiveNotification,
        clearAllNotifications,
        updateNotificationSettings,
        notifyLowStock,
        notifyExpiry,
        notifyNewOrder,
        notifyNewUser,
        notifyReportGenerated,
        notifyNewContact,
        requestNotificationPermission,
        fetchNotifications,
        fetchUnreadCount,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => useContext(NotificationContext);
