'use client';

import { useState, useMemo, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Plus,
  Search,
  MoreVertical,
  Activity,
  Package,
  DollarSign,
  Trash,
  Pencil,
  ArrowLeft,
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import Button from '../../components/Button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '../../components/management-system/ui/Dropdown-menu';
import { BarChart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from 'recharts';
import { format } from 'date-fns';

const DrugPurchasesPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();
  const [searchTerm, setSearchTerm] = useState('');
  const [purchases, setPurchases] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadPurchases();
  }, []);

  const loadPurchases = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('http://localhost:5432/api/v1/drug-purchases');
      if (!response.ok) {
        throw new Error('Failed to fetch purchases');
      }

      const data = await response.json();
      if (data.success && Array.isArray(data.data)) {
        setPurchases(data.data);
      } else {
        setPurchases([]);
      }
    } catch (error) {
      console.error('Error loading purchases:', error);
      setError('Failed to load purchases');
      setPurchases([]);
    } finally {
      setLoading(false);
    }
  };

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const filteredPurchases = useMemo(() => {
    if (!Array.isArray(purchases)) return [];
    return purchases.filter((purchase) => {
      if (!purchase) return false;
      const drugName = purchase.drug_name || '';
      const supplier = purchase.supplier || '';
      const matchesSearch = drugName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           supplier.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [purchases, searchTerm]);

  const chartData = useMemo(() => {
    if (!Array.isArray(filteredPurchases)) return [];
    return filteredPurchases.map((purchase) => ({
      name: purchase.drug_name || 'Unknown',
      quantity: purchase.quantity || 0,
      amount: purchase.total_amount || 0,
    }));
  }, [filteredPurchases]);

  const totalPurchases = Array.isArray(purchases) ? purchases.length : 0;
  const totalQuantity = Array.isArray(purchases) ? purchases.reduce((sum, p) => sum + (Number(p.quantity) || 0), 0) : 0;
  const totalAmount = Array.isArray(purchases) ? purchases.reduce((sum, p) => sum + (Number(p.total_amount) || 0), 0) : 0;

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this purchase?')) {
      try {
        const response = await fetch(`http://localhost:5432/api/v1/drug-purchases/${id}`, {
          method: 'DELETE',
        });

        if (response.ok) {
          setPurchases(prev => prev.filter(p => p.id !== id));
          alert('Purchase deleted successfully!');
        } else {
          alert('Failed to delete purchase');
        }
      } catch (error) {
        console.error('Error deleting purchase:', error);
        alert('Failed to delete purchase');
      }
    }
  };

  const handleEdit = (id) => {
    navigate(`/admin/drugs/purchases/edit/${id}`);
  };

  const handleAdd = () => {
    navigate('/admin/drugs/purchases/add');
  };

  if (loading) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
              <p className="mt-4 text-gray-600 dark:text-gray-400">Loading purchases...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
        <div className="max-w-7xl mx-auto space-y-6">
          <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/admin/drugs')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Drugs
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Drug Purchases</h1>
            </div>
          </div>
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-red-600 dark:text-red-400">{error}</p>
              <Button onClick={loadPurchases} className="mt-4">
                Retry
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Drugs
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Drug Purchases</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Manage drug purchases and inventory</p>
          </div>
        </div>

        <div
          className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
        >
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button onClick={loadPurchases} variant="secondary" size="sm">
              Refresh
            </Button>
            <Button onClick={handleAdd} variant="primary" className="border border-orange-600">
              <Plus className={`h-4 w-4 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
              Purchase Drugs
            </Button>
          </div>
        </div>

        {/* Search */}
        <div className={`flex flex-col sm:flex-row gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="relative flex-1">
            <Search
              className={`absolute ${language === 'ps' ? 'right-3' : 'left-3'} top-1/2 transform -translate-y-1/2 text-gray-400`}
              size={20}
            />
            <input
              type="text"
              placeholder="Search purchases..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full ${language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4'} py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white`}
              dir={language === 'ps' ? 'rtl' : 'ltr'}
            />
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
              <Activity className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalPurchases}</div>
              <p className="text-xs text-muted-foreground">Purchase orders</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Total Quantity</CardTitle>
              <Package className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalQuantity}</div>
              <p className="text-xs text-muted-foreground">Units purchased</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader
              className={`flex flex-row items-center justify-between space-y-0 pb-2 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
            >
              <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
              <DollarSign className={`h-4 w-4 text-muted-foreground ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${(totalAmount || 0).toFixed(2)}</div>
              <p className="text-xs text-muted-foreground">Total spent</p>
            </CardContent>
          </Card>
        </div>

        {/* Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Purchase Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-[300px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="quantity" fill="#8B5CF6" name="Quantity" />
                  <Bar dataKey="amount" fill="#3B82F6" name="Amount ($)" />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Purchases Table */}
        <Card>
          <CardHeader>
            <CardTitle>Purchase History</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Drug Name</TableHead>
                  <TableHead>Quantity</TableHead>
                  <TableHead>Transferred</TableHead>
                  <TableHead>Remaining</TableHead>
                  <TableHead>Price/Unit</TableHead>
                  <TableHead>Total Amount</TableHead>
                  <TableHead>Supplier</TableHead>
                  <TableHead>Purchase Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className={`text-${language === 'ps' ? 'left' : 'right'}`}>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPurchases.length > 0 ? (
                  filteredPurchases.map((purchase) => (
                    <TableRow key={purchase.id || Math.random()}>
                      <TableCell className="font-medium">{purchase.drug_name || 'N/A'}</TableCell>
                      <TableCell>{purchase.quantity || 0}</TableCell>
                      <TableCell>
                        <span className="text-blue-600 font-medium">
                          {purchase.transferred_quantity || 0}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${
                          (purchase.remaining_quantity || 0) > 0
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}>
                          {purchase.remaining_quantity || 0}
                        </span>
                        {(purchase.remaining_quantity || 0) === 0 && (
                          <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs rounded-full">
                            Completed
                          </span>
                        )}
                      </TableCell>
                      <TableCell>${(purchase.price_per_unit || 0)}</TableCell>
                      <TableCell>${(purchase.total_amount || 0)}</TableCell>
                      <TableCell>{purchase.supplier || 'N/A'}</TableCell>
                      <TableCell>
                        {purchase.purchase_date
                          ? format(new Date(purchase.purchase_date), 'yyyy-MM-dd')
                          : 'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          purchase.status === 'Completed'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {purchase.status || 'Pending'}
                        </span>
                      </TableCell>
                      <TableCell className={`text-${language === 'ps' ? 'left' : 'right'}`}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <div
                              className={`flex items-center justify-center w-8 h-8 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 ${language === 'ps' ? 'flex-row-reverse' : ''}`}
                            >
                              <MoreVertical className="h-4 w-4" />
                            </div>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'}>
                            <DropdownMenuItem onClick={() => handleEdit(purchase.id)}>
                              <Pencil className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleDelete(purchase.id)}>
                              <Trash className={`h-4 w-4 text-orange-600 ${language === 'ps' ? 'ml-2' : 'mr-2'}`} />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-4">
                      No purchases found
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DrugPurchasesPage;
