-- Create Notifications table
CREATE TABLE IF NOT EXISTS Notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'system',
    contact_id INT NULL,
    user_id INT NULL,
    data JSON NULL,
    read_status BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (contact_id) REFERENCES ContactUs(C_Id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(u_Id) ON DELETE CASCADE,
    
    -- Indexes for better performance
    INDEX idx_category (category),
    INDEX idx_read_status (read_status),
    INDEX idx_created_at (created_at),
    INDEX idx_contact_id (contact_id),
    INDEX idx_user_id (user_id)
);

-- Insert some sample notifications for testing (optional)
-- INSERT INTO Notifications (type, title, message, category, data) VALUES
-- ('system', 'Welcome', 'Welcome to the admin panel', 'system', '{}'),
-- ('info', 'System Update', 'System has been updated successfully', 'system', '{}');
