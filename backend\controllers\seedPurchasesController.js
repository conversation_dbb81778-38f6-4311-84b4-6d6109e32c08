import SeedPurchasesModel from "../models/seedPurchasesModel.js";
import <PERSON><PERSON> from "joi";

// Validation schema
const seedPurchaseSchema = Joi.object({
  seed_id: Joi.number().integer().optional().allow(null),
  seed_name: Joi.string().min(3).max(100).required(),
  quantity: Joi.number().integer().min(1).required(),
  price_per_unit: Joi.number().min(0).required(),
  total_amount: Joi.number().min(0).required(),
  supplier: Joi.string().min(3).max(100).required(),
  purchase_date: Joi.date().required(),
  status: Joi.string().valid('Pending', 'Completed', 'Cancelled').optional(),
  notes: Joi.string().max(500).optional().allow('', null),
});

const SeedPurchasesController = {
  // Get all seed purchases with remaining quantities
  getAll: async (req, res) => {
    try {
      const purchases = await SeedPurchasesModel.getAllWithRemainingQuantity();
      res.status(200).json({
        success: true,
        total: purchases.length,
        data: purchases,
      });
    } catch (error) {
      console.error("Error fetching seed purchases:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch seed purchases",
      });
    }
  },

  // Get seed purchase by ID
  getById: async (req, res) => {
    try {
      const { id } = req.params;
      const purchase = await SeedPurchasesModel.getById(id);

      if (!purchase) {
        return res.status(404).json({
          success: false,
          error: "Seed purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        data: purchase,
      });
    } catch (error) {
      console.error("Error fetching seed purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch seed purchase",
      });
    }
  },

  // Create new seed purchase
  create: async (req, res) => {
    try {
      const { error, value } = seedPurchaseSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
      }

      const newPurchase = await SeedPurchasesModel.create(value);
      res.status(201).json({
        success: true,
        data: newPurchase,
      });
    } catch (error) {
      console.error("Error creating seed purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create seed purchase",
      });
    }
  },

  // Update seed purchase
  update: async (req, res) => {
    try {
      const { id } = req.params;
      const { error, value } = seedPurchaseSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
      }

      const updatedPurchase = await SeedPurchasesModel.update(id, value);

      if (!updatedPurchase) {
        return res.status(404).json({
          success: false,
          error: "Seed purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        data: updatedPurchase,
      });
    } catch (error) {
      console.error("Error updating seed purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update seed purchase",
      });
    }
  },

  // Delete seed purchase
  delete: async (req, res) => {
    try {
      const { id } = req.params;
      const result = await SeedPurchasesModel.delete(id);

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          error: "Seed purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        message: "Seed purchase deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting seed purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to delete seed purchase",
      });
    }
  },

  // Get purchases by seed ID
  getBySeedId: async (req, res) => {
    try {
      const { seedId } = req.params;
      const purchases = await SeedPurchasesModel.getBySeedId(seedId);
      
      res.status(200).json({
        success: true,
        total: purchases.length,
        data: purchases,
      });
    } catch (error) {
      console.error("Error fetching seed purchases by seed ID:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch seed purchases",
      });
    }
  },

  // Get purchase statistics
  getStatistics: async (req, res) => {
    try {
      const stats = await SeedPurchasesModel.getStatistics();
      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("Error fetching purchase statistics:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch statistics",
      });
    }
  },
};

export default SeedPurchasesController;
