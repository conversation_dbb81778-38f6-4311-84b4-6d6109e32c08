import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ArrowLeft, 
  Download, 
  Filter, 
  BarChart3, 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  TrendingUp,
  Save,
  Printer
} from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/feed-components/Table';
import Button from '../../components/Button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Pie<PERSON>hart as RechartsPieChart,
  Pie,
  Cell,
  LineChart as RechartsLineChart,
  Line,
  ComposedChart,
  Area
} from 'recharts';
import { format } from 'date-fns';

const SeedReportsPage = () => {
  const navigate = useNavigate();
  const { language, translations } = useLanguage();
  const [reportData, setReportData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [chartTimeFilter, setChartTimeFilter] = useState('month');
  const [summary, setSummary] = useState({
    totalPurchases: 0,
    completedTransfers: 0,
    totalInvestment: 0,
    totalValue: 0,
    totalProfit: 0,
    profitMargin: 0
  });

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'AFN',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount || 0).replace('AFN', 'AFN ');
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch (error) {
      return 'Invalid Date';
    }
  };

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      
      // Fetch purchases and transfers data
      const [purchasesResponse, transfersResponse] = await Promise.all([
        fetch('http://localhost:5432/api/v1/seed-purchases'),
        fetch('http://localhost:5432/api/v1/seed-transfers')
      ]);

      const purchasesData = await purchasesResponse.json();
      const transfersData = await transfersResponse.json();

      if (purchasesData.success && transfersData.success) {
        const purchases = purchasesData.data || [];
        const transfers = transfersData.data || [];

        // Process data to create comprehensive reports
        const processedData = purchases.map(purchase => {
          const relatedTransfers = transfers.filter(transfer => 
            transfer.seed_id === purchase.id
          );

          const totalTransferred = relatedTransfers.reduce((sum, transfer) => 
            sum + (transfer.quantity || 0), 0
          );

          const remainingQuantity = (purchase.quantity || 0) - totalTransferred;
          const transferValue = relatedTransfers.reduce((sum, transfer) => 
            sum + ((transfer.quantity || 0) * (purchase.price_per_unit || 0)), 0
          );

          // Calculate profit/loss
          const investmentCost = purchase.total_amount || 0;
          const currentValue = transferValue + (remainingQuantity * (purchase.price_per_unit || 0));
          const profit = currentValue - investmentCost;
          const profitMargin = investmentCost > 0 ? (profit / investmentCost) * 100 : 0;

          return {
            id: purchase.id,
            seedName: purchase.seed_name,
            supplier: purchase.supplier,
            purchaseDate: purchase.purchase_date,
            quantity: purchase.quantity,
            pricePerUnit: purchase.price_per_unit,
            totalAmount: purchase.total_amount,
            transferred: totalTransferred,
            remaining: remainingQuantity,
            transferValue: transferValue,
            currentValue: currentValue,
            profit: profit,
            profitMargin: profitMargin,
            status: remainingQuantity === 0 ? 'completed' : 
                   totalTransferred > 0 ? 'in_progress' : 'pending',
            transfers: relatedTransfers
          };
        });

        setReportData(processedData);

        // Calculate summary
        const totalPurchases = processedData.length;
        const completedTransfers = processedData.filter(item => item.status === 'completed').length;
        const totalInvestment = processedData.reduce((sum, item) => sum + item.totalAmount, 0);
        const totalValue = processedData.reduce((sum, item) => sum + item.currentValue, 0);
        const totalProfit = processedData.reduce((sum, item) => sum + item.profit, 0);
        const profitMargin = totalInvestment > 0 ? (totalProfit / totalInvestment) * 100 : 0;

        setSummary({
          totalPurchases,
          completedTransfers,
          totalInvestment,
          totalValue,
          totalProfit,
          profitMargin
        });
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredData = reportData.filter(item => {
    if (statusFilter === 'all') return true;
    return item.status === statusFilter;
  });

  const exportToCSV = () => {
    const headers = [
      'Seed Name', 'Supplier', 'Purchase Date', 'Quantity', 'Price/Unit', 
      'Total Amount', 'Transferred', 'Remaining', 'Current Value', 'Profit/Loss', 'Status'
    ];
    
    const csvContent = [
      headers.join(','),
      ...filteredData.map(item => [
        item.seedName,
        item.supplier,
        formatDate(item.purchaseDate),
        item.quantity,
        item.pricePerUnit,
        item.totalAmount,
        item.transferred,
        item.remaining,
        item.currentValue,
        item.profit,
        item.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `seed-reports-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    window.print();
  };

  const getProfitIcon = (profit) => {
    if (profit > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (profit < 0) return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      completed: { bg: 'bg-green-100', text: 'text-green-800', label: 'Completed' },
      in_progress: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'In Progress' },
      pending: { bg: 'bg-gray-100', text: 'text-gray-800', label: 'Pending' }
    };
    
    const config = statusConfig[status] || statusConfig.pending;
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.bg} ${config.text}`}>
        {config.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-lg text-gray-600">Loading seed reports...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className={`flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <div className="flex items-center gap-4">
            <Button
              variant="secondary"
              onClick={() => navigate('/admin/seeds')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {t('back') || 'Back'}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t('seed_reports') || 'Seed Reports'}
              </h1>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                {t('seed_reports_desc') || 'Comprehensive analysis of seed purchases, transfers, and profitability'}
              </p>
            </div>
          </div>
          
          <div className={`flex items-center gap-3 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
            <Button
              variant="secondary"
              onClick={exportToCSV}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {t('export_csv') || 'Export CSV'}
            </Button>
            <Button
              variant="secondary"
              onClick={exportToPDF}
              className="flex items-center gap-2"
            >
              <Printer className="h-4 w-4" />
              {t('print') || 'Print'}
            </Button>
          </div>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Purchases</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.totalPurchases}</div>
              <p className="text-xs text-muted-foreground">
                {summary.completedTransfers} completed transfers
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Investment</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalInvestment)}</div>
              <p className="text-xs text-muted-foreground">
                Purchase costs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Value</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(summary.totalValue)}</div>
              <p className="text-xs text-muted-foreground">
                Market value
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Profit</CardTitle>
              <TrendingUp className={`h-4 w-4 ${summary.totalProfit >= 0 ? 'text-green-500' : 'text-red-500'}`} />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${summary.totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatCurrency(summary.totalProfit)}
              </div>
              <p className="text-xs text-muted-foreground">
                {summary.profitMargin.toFixed(1)}% margin
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filter Controls */}
        <Card className="no-print">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Filter className="h-5 w-5 mr-2" />
                {t('filter_by_status') || 'Filter by Status'}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-2">
              {[
                { value: 'all', label: t('all') || 'All' },
                { value: 'completed', label: t('completed') || 'Completed' },
                { value: 'in_progress', label: t('in_progress') || 'In Progress' },
                { value: 'pending', label: t('pending') || 'Pending' }
              ].map((option) => (
                <button
                  key={option.value}
                  onClick={() => setStatusFilter(option.value)}
                  className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    statusFilter === option.value
                      ? 'bg-[#FF6B00] text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Charts Section */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              {t('process_analytics_dashboard') || 'Process Analytics Dashboard'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Status Distribution Chart */}
              <div className="h-80">
                <h3 className="text-lg font-semibold mb-4">Status Distribution</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={[
                        { name: 'Completed', value: reportData.filter(item => item.status === 'completed').length, color: '#388E3C' },
                        { name: 'In Progress', value: reportData.filter(item => item.status === 'in_progress').length, color: '#FF6B00' },
                        { name: 'Pending', value: reportData.filter(item => item.status === 'pending').length, color: '#FFC107' }
                      ]}
                      cx="50%"
                      cy="50%"
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(1)}%`}
                    >
                      {[
                        { name: 'Completed', value: reportData.filter(item => item.status === 'completed').length, color: '#388E3C' },
                        { name: 'In Progress', value: reportData.filter(item => item.status === 'in_progress').length, color: '#FF6B00' },
                        { name: 'Pending', value: reportData.filter(item => item.status === 'pending').length, color: '#FFC107' }
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>

              {/* Profit Analysis Chart */}
              <div className="h-80">
                <h3 className="text-lg font-semibold mb-4">Profit Analysis</h3>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={filteredData.slice(0, 10)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="seedName" angle={-45} textAnchor="end" height={80} />
                    <YAxis />
                    <Tooltip formatter={(value) => formatCurrency(value)} />
                    <Legend />
                    <Bar dataKey="totalAmount" fill="#FF6B00" name="Investment" />
                    <Bar dataKey="currentValue" fill="#388E3C" name="Current Value" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Detailed Analysis Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                {t('detailed_process_analysis') || 'Detailed Process Analysis'}
              </div>
              <div className="text-sm text-gray-500">
                {filteredData.length} {t('records') || 'records'}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('seed_name') || 'Seed Name'}</TableHead>
                    <TableHead>{t('supplier') || 'Supplier'}</TableHead>
                    <TableHead>{t('purchase_date') || 'Purchase Date'}</TableHead>
                    <TableHead>{t('quantity') || 'Quantity'}</TableHead>
                    <TableHead>{t('transferred') || 'Transferred'}</TableHead>
                    <TableHead>{t('remaining') || 'Remaining'}</TableHead>
                    <TableHead>{t('investment') || 'Investment'}</TableHead>
                    <TableHead>{t('current_value') || 'Current Value'}</TableHead>
                    <TableHead>{t('profit_loss') || 'Profit/Loss'}</TableHead>
                    <TableHead>{t('status') || 'Status'}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell className="font-medium">{item.seedName}</TableCell>
                      <TableCell>{item.supplier}</TableCell>
                      <TableCell>{formatDate(item.purchaseDate)}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>
                        <span className="text-blue-600 font-medium">{item.transferred}</span>
                      </TableCell>
                      <TableCell>
                        <span className={`font-medium ${item.remaining > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {item.remaining}
                        </span>
                      </TableCell>
                      <TableCell>{formatCurrency(item.totalAmount)}</TableCell>
                      <TableCell>{formatCurrency(item.currentValue)}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          {getProfitIcon(item.profit)}
                          <div className="ml-2">
                            <div className={`font-medium ${item.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatCurrency(item.profit)}
                            </div>
                            <div className={`text-xs ${item.profit >= 0 ? 'text-green-500' : 'text-red-500'}`}>
                              {item.profitMargin.toFixed(1)}%
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(item.status)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SeedReportsPage;
