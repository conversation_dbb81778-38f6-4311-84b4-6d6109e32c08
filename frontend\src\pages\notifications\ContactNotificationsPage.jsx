import React, { useState, useEffect } from 'react';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';
import { Card, List, Button, Badge, Empty, Modal, Descriptions, Space, Tag, Popconfirm } from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  EyeOutlined,
  MailOutlined,
  PhoneOutlined,
  UserOutlined,
  MessageOutlined,
  CalendarOutlined,
} from '@ant-design/icons';
import axios from 'axios';

const ContactNotificationsPage = () => {
  const { language, translations } = useLanguage();
  const { notifications, markAsRead, clearNotification, fetchNotifications } = useNotifications();
  const [contactDetails, setContactDetails] = useState(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [allContacts, setAllContacts] = useState([]);
  const [loading, setLoading] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // Filter contact notifications
  const contactNotifications = notifications.filter(n => n.category === 'contacts');

  // Fetch all contact submissions
  const fetchAllContacts = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5432/api/v1/contact-us', {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setAllContacts(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch contact details
  const fetchContactDetails = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      if (response.data.success) {
        setContactDetails(response.data.data);
        setShowContactModal(true);
      }
    } catch (error) {
      console.error('Error fetching contact details:', error);
    }
  };

  // Delete contact
  const deleteContact = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      
      // Refresh contacts list
      fetchAllContacts();
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  useEffect(() => {
    fetchAllContacts();
  }, []);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (read) => {
    return read ? 'default' : 'processing';
  };

  const getStatusText = (read) => {
    return read ? 'Read' : 'Unread';
  };

  return (
    <div className="contact-notifications-page p-6">
      <Card
        title={
          <Space>
            <MailOutlined />
            Contact Form Notifications
            <Badge count={contactNotifications.filter(n => !n.read).length} />
          </Space>
        }
        extra={
          <Button 
            type="primary" 
            icon={<CheckOutlined />}
            onClick={() => {
              contactNotifications.forEach(n => {
                if (!n.read) markAsRead(n.id);
              });
            }}
            disabled={!contactNotifications.some(n => !n.read)}
          >
            Mark All Read
          </Button>
        }
      >
        {/* Recent Notifications */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-4">Recent Notifications</h3>
          {contactNotifications.length === 0 ? (
            <Empty description="No contact notifications" />
          ) : (
            <List
              itemLayout="horizontal"
              dataSource={contactNotifications.slice(0, 5)}
              renderItem={(notification) => (
                <List.Item
                  actions={[
                    !notification.read && (
                      <Button type="link" onClick={() => markAsRead(notification.id)}>
                        Mark Read
                      </Button>
                    ),
                    <Button 
                      type="link" 
                      icon={<EyeOutlined />}
                      onClick={() => fetchContactDetails(notification.data.contactId)}
                    >
                      View
                    </Button>,
                    <Button 
                      type="link" 
                      danger 
                      onClick={() => clearNotification(notification.id)}
                    >
                      Delete
                    </Button>,
                  ]}
                >
                  <List.Item.Meta
                    avatar={<BellOutlined style={{ fontSize: 20, color: notification.read ? '#ccc' : '#1890ff' }} />}
                    title={
                      <Space>
                        <span>{notification.message}</span>
                        <Tag color={getStatusColor(notification.read)}>
                          {getStatusText(notification.read)}
                        </Tag>
                      </Space>
                    }
                    description={
                      <Space>
                        <CalendarOutlined />
                        {formatDate(notification.timestamp)}
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          )}
        </div>

        {/* All Contact Submissions */}
        <div>
          <h3 className="text-lg font-semibold mb-4">All Contact Submissions</h3>
          <List
            loading={loading}
            itemLayout="horizontal"
            dataSource={allContacts}
            renderItem={(contact) => (
              <List.Item
                actions={[
                  <Button 
                    type="link" 
                    icon={<EyeOutlined />}
                    onClick={() => {
                      setContactDetails(contact);
                      setShowContactModal(true);
                    }}
                  >
                    View Details
                  </Button>,
                  <Popconfirm
                    title="Are you sure you want to delete this contact?"
                    onConfirm={() => deleteContact(contact.C_Id)}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button type="link" danger icon={<DeleteOutlined />}>
                      Delete
                    </Button>
                  </Popconfirm>,
                ]}
              >
                <List.Item.Meta
                  avatar={<UserOutlined style={{ fontSize: 20 }} />}
                  title={
                    <Space>
                      <span>{contact.user_name}</span>
                      <span className="text-gray-500">-</span>
                      <span>{contact.C_Title}</span>
                    </Space>
                  }
                  description={
                    <Space>
                      <MailOutlined />
                      {contact.user_email}
                      {contact.user_phone && (
                        <>
                          <PhoneOutlined />
                          {contact.user_phone}
                        </>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        </div>
      </Card>

      {/* Contact Details Modal */}
      <Modal
        title="Contact Details"
        open={showContactModal}
        onCancel={() => {
          setShowContactModal(false);
          setContactDetails(null);
        }}
        footer={[
          <Button key="close" onClick={() => setShowContactModal(false)}>
            Close
          </Button>,
          contactDetails && (
            <Popconfirm
              key="delete"
              title="Are you sure you want to delete this contact?"
              onConfirm={() => {
                deleteContact(contactDetails.C_Id);
                setShowContactModal(false);
                setContactDetails(null);
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                Delete Contact
              </Button>
            </Popconfirm>
          ),
        ]}
        width={600}
      >
        {contactDetails && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Name">
              <Space>
                <UserOutlined />
                {contactDetails.user_name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              <Space>
                <MailOutlined />
                {contactDetails.user_email}
              </Space>
            </Descriptions.Item>
            {contactDetails.user_phone && (
              <Descriptions.Item label="Phone">
                <Space>
                  <PhoneOutlined />
                  {contactDetails.user_phone}
                </Space>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="Subject">
              {contactDetails.C_Title}
            </Descriptions.Item>
            <Descriptions.Item label="Message">
              <div className="whitespace-pre-wrap">
                {contactDetails.C_Body}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="Submitted">
              <Space>
                <CalendarOutlined />
                {formatDate(contactDetails.created_at || new Date())}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ContactNotificationsPage;
