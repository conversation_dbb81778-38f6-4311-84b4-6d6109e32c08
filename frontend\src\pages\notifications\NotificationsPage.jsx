/* eslint-disable react/jsx-key */
import { useState, useEffect } from 'react';
import { Card, List, Button, Space, Tag, Badge, Empty, Popconfirm, Modal, Descriptions } from 'antd';
import { CheckOutlined, DeleteOutlined, ReloadOutlined, EyeOutlined, MailOutlined, PhoneOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';
import axios from 'axios';

const NotificationsPage = () => {
  const { language, translations } = useLanguage();
  const {
    notifications,
    notificationSettings,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    archiveNotification,
    updateNotificationSettings,
    fetchNotifications,
    fetchUnreadCount,
  } = useNotifications();

  const [activeTab, setActiveTab] = useState('contacts');

  const [contactDetails, setContactDetails] = useState(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [allContacts, setAllContacts] = useState([]);
  const [loading, setLoading] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // Fetch all contact submissions
  const fetchAllContacts = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5432/api/v1/contact-us', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setAllContacts(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch contact details
  const fetchContactDetails = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setContactDetails(response.data.data);
        setShowContactModal(true);
      }
    } catch (error) {
      console.error('Error fetching contact details:', error);
    }
  };

  // Delete contact
  const deleteContact = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh contacts list and notifications
      fetchAllContacts();
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  useEffect(() => {
    fetchAllContacts();
    // Debug: Log notifications to see what we have
    console.log('📋 Current notifications:', notifications);
    console.log('📋 Contact notifications:', notifications.filter(n => n.category === 'contacts'));
  }, [notifications]);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'low_stock':
        return <Tag color="red">{t('low_stock')}</Tag>;
      case 'expiry_alert':
        return <Tag color="orange">{t('expiry_alert')}</Tag>;
      case 'new_order':
        return <Tag color="blue">{t('new_order')}</Tag>;
      case 'new_user':
        return <Tag color="green">{t('new_user')}</Tag>;
      case 'new_contact':
        return <Tag color="cyan">Contact Form</Tag>;
      case 'report_generated':
        return <Tag color="purple">{t('report_generated')}</Tag>;
      default:
        return <BellOutlined />;
    }
  };

  // Only show contact notifications
  const filteredNotifications = notifications.filter((notification) => {
    return notification.category === 'contacts' && !notification.archived;
  });



  const contactNotificationsCount = notifications.filter(n => n.category === 'contacts' && !n.archived).length;

  // Only show Contacts tab
  const tabItems = [
    {
      key: 'contacts',
      label: (
        <Space>
          <MailOutlined />
          Contacts
          {contactNotificationsCount > 0 && (
            <Badge count={contactNotificationsCount} size="small" />
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="notifications-page">
      <Card
        title={
          <Space>
            <MailOutlined />
            Contact Form Notifications
            <Badge count={contactNotificationsCount} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                console.log('🔄 Manual refresh triggered');
                fetchNotifications();
                fetchUnreadCount();
              }}
            >
              Refresh
            </Button>

            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={() => {
                // Mark all contact notifications as read
                notifications
                  .filter(n => n.category === 'contacts' && !n.read)
                  .forEach(n => markAsRead(n.id));
              }}
              disabled={!notifications.some((n) => n.category === 'contacts' && !n.read && !n.archived)}
            >
              Mark All Read
            </Button>

            <Popconfirm
              title="Are you sure you want to clear all contact notifications?"
              onConfirm={() => {
                // Clear only contact notifications
                notifications
                  .filter(n => n.category === 'contacts')
                  .forEach(n => clearNotification(n.id));
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                Clear All Contacts
              </Button>
            </Popconfirm>
          </Space>
        }
      >


        {/* Contact notifications header */}
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <Space>
            <MailOutlined className="text-blue-600" />
            <span className="font-medium text-blue-800">Contact Form Submissions</span>
            <Badge count={contactNotificationsCount} />
          </Space>
          <div className="mt-2 text-sm text-blue-600">
            Manage contact form submissions and notifications. Click "View Details" to see full contact information.
          </div>
        </div>

        {/* Show notifications if they exist, otherwise show contact submissions directly */}
        {filteredNotifications.length === 0 ? (
          <div>
            {/* If no notifications but we have contacts, show them directly */}
            {allContacts.length > 0 ? (
              <div>
                <h3 className="text-lg font-semibold mb-4">All Contact Submissions</h3>
                <List
                  itemLayout="horizontal"
                  dataSource={allContacts}
                  renderItem={(contact) => (
                    <List.Item
                      actions={[
                        <Button
                          type="link"
                          icon={<EyeOutlined />}
                          onClick={() => {
                            setContactDetails(contact);
                            setShowContactModal(true);
                          }}
                        >
                          View Details
                        </Button>,
                        <Popconfirm
                          title="Are you sure you want to delete this contact?"
                          onConfirm={() => deleteContact(contact.C_Id)}
                          okText="Yes"
                          cancelText="No"
                        >
                          <Button type="link" danger icon={<DeleteOutlined />}>
                            Delete
                          </Button>
                        </Popconfirm>,
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<UserOutlined style={{ fontSize: 20 }} />}
                        title={
                          <Space>
                            <span>{contact.user_name}</span>
                            <span className="text-gray-500">-</span>
                            <span>{contact.C_Title}</span>
                          </Space>
                        }
                        description={
                          <Space>
                            <MailOutlined />
                            {contact.user_email}
                            {contact.user_phone && (
                              <>
                                <PhoneOutlined />
                                {contact.user_phone}
                              </>
                            )}
                          </Space>
                        }
                      />
                    </List.Item>
                  )}
                />
              </div>
            ) : (
              <Empty
                image={<MailOutlined style={{ fontSize: 48, color: '#1890ff' }} />}
                description="No contact form submissions yet"
                className="py-8"
              />
            )}
          </div>
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={filteredNotifications}
            renderItem={(notification) => (
              <List.Item
                actions={[
                  // Contact-specific view details button
                  notification.type === 'new_contact' && notification.data.contactId && (
                    <Button
                      type="link"
                      icon={<EyeOutlined />}
                      onClick={() => fetchContactDetails(notification.data.contactId)}
                    >
                      View Details
                    </Button>
                  ),
                  !notification.read && (
                    <Button type="link" onClick={() => markAsRead(notification.id)}>
                      {t('mark_read')}
                    </Button>
                  ),
                  <Button type="link" danger onClick={() => clearNotification(notification.id)}>
                    {t('delete')}
                  </Button>,
                  <Button type="link" onClick={() => archiveNotification(notification.id)}>
                    {t('archive')}
                  </Button>,
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.type)}
                  title={
                    <Space>
                      <span>{notification.title || notification.message}</span>
                      {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                    </Space>
                  }
                  description={
                    <div>
                      <div>{notification.message}</div>
                      {notification.type === 'new_contact' && notification.data && (
                        <div className="mt-2 text-sm text-gray-500">
                          <Space>
                            <UserOutlined />
                            {notification.data.userName}
                            <MailOutlined />
                            {notification.data.userEmail}
                            {notification.data.phone && (
                              <>
                                <PhoneOutlined />
                                {notification.data.phone}
                              </>
                            )}
                          </Space>
                        </div>
                      )}
                      <div className="mt-1 text-xs text-gray-400">
                        <CalendarOutlined /> {new Date(notification.timestamp).toLocaleString()}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>

      {/* Contact Details Modal */}
      <Modal
        title="Contact Details"
        open={showContactModal}
        onCancel={() => {
          setShowContactModal(false);
          setContactDetails(null);
        }}
        footer={[
          <Button key="close" onClick={() => setShowContactModal(false)}>
            Close
          </Button>,
          contactDetails && (
            <Popconfirm
              key="delete"
              title="Are you sure you want to delete this contact?"
              onConfirm={() => {
                deleteContact(contactDetails.C_Id);
                setShowContactModal(false);
                setContactDetails(null);
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                Delete Contact
              </Button>
            </Popconfirm>
          ),
        ]}
        width={600}
      >
        {contactDetails && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Name">
              <Space>
                <UserOutlined />
                {contactDetails.user_name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              <Space>
                <MailOutlined />
                {contactDetails.user_email}
              </Space>
            </Descriptions.Item>
            {contactDetails.user_phone && (
              <Descriptions.Item label="Phone">
                <Space>
                  <PhoneOutlined />
                  {contactDetails.user_phone}
                </Space>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="Subject">
              {contactDetails.C_Title}
            </Descriptions.Item>
            <Descriptions.Item label="Message">
              <div className="whitespace-pre-wrap">
                {contactDetails.C_Body}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="Submitted">
              <Space>
                <CalendarOutlined />
                {new Date(contactDetails.created_at || new Date()).toLocaleString()}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default NotificationsPage;
