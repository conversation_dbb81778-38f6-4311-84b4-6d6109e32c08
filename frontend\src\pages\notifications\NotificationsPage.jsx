/* eslint-disable react/jsx-key */
import { useState, useEffect } from 'react';
import { Card, List, Button, Space, Tag, Badge, Empty, Tabs, Switch, Divider, Popconfirm, Modal, Descriptions } from 'antd';
import { BellOutlined, CheckOutlined, DeleteOutlined, SettingOutlined, InboxOutlined, ReloadOutlined, EyeOutlined, MailOutlined, PhoneOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';
import { useLanguage } from '../../contexts/LanguageContext';
import { useNotifications } from '../../contexts/NotificationContext';
import axios from 'axios';

const NotificationsPage = () => {
  const { language, translations } = useLanguage();
  const {
    notifications,
    notificationSettings,
    markAsRead,
    markAllAsRead,
    clearNotification,
    clearAllNotifications,
    archiveNotification,
    updateNotificationSettings,
    fetchNotifications,
    fetchUnreadCount,
  } = useNotifications();

  const [activeTab, setActiveTab] = useState('all');
  const [showSettings, setShowSettings] = useState(false);
  const [contactDetails, setContactDetails] = useState(null);
  const [showContactModal, setShowContactModal] = useState(false);
  const [allContacts, setAllContacts] = useState([]);
  const [loading, setLoading] = useState(false);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  // Fetch all contact submissions
  const fetchAllContacts = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get('http://localhost:5432/api/v1/contact-us', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setAllContacts(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching contacts:', error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch contact details
  const fetchContactDetails = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        setContactDetails(response.data.data);
        setShowContactModal(true);
      }
    } catch (error) {
      console.error('Error fetching contact details:', error);
    }
  };

  // Delete contact
  const deleteContact = async (contactId) => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`http://localhost:5432/api/v1/contact-us/${contactId}`, {
        headers: { Authorization: `Bearer ${token}` }
      });

      // Refresh contacts list and notifications
      fetchAllContacts();
      fetchNotifications();
    } catch (error) {
      console.error('Error deleting contact:', error);
    }
  };

  useEffect(() => {
    fetchAllContacts();
    // Debug: Log notifications to see what we have
    console.log('📋 Current notifications:', notifications);
    console.log('📋 Contact notifications:', notifications.filter(n => n.category === 'contacts'));
  }, [notifications]);

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'low_stock':
        return <Tag color="red">{t('low_stock')}</Tag>;
      case 'expiry_alert':
        return <Tag color="orange">{t('expiry_alert')}</Tag>;
      case 'new_order':
        return <Tag color="blue">{t('new_order')}</Tag>;
      case 'new_user':
        return <Tag color="green">{t('new_user')}</Tag>;
      case 'new_contact':
        return <Tag color="cyan">Contact Form</Tag>;
      case 'report_generated':
        return <Tag color="purple">{t('report_generated')}</Tag>;
      default:
        return <BellOutlined />;
    }
  };

  const filteredNotifications = notifications.filter((notification) => {
    if (activeTab === 'all') return !notification.archived;
    if (activeTab === 'unread') return !notification.read && !notification.archived;
    if (activeTab === 'archived') return notification.archived;
    return notification.category === activeTab && !notification.archived;
  });

  const handleSettingsChange = (key, value) => {
    updateNotificationSettings({ [key]: value });
  };

  const handleCategoryChange = (category, value) => {
    updateNotificationSettings({
      categories: {
        ...notificationSettings.categories,
        [category]: value,
      },
    });
  };

  const contactNotificationsCount = notifications.filter(n => n.category === 'contacts' && !n.archived).length;

  const tabItems = [
    {
      key: 'all',
      label: `All (${notifications.filter(n => !n.archived).length})`,
    },
    {
      key: 'unread',
      label: `Unread (${notifications.filter(n => !n.read && !n.archived).length})`,
    },
    {
      key: 'contacts',
      label: (
        <Space>
          <MailOutlined />
          Contacts
          {contactNotificationsCount > 0 && (
            <Badge count={contactNotificationsCount} size="small" />
          )}
        </Space>
      ),
    },
    {
      key: 'archived',
      label: t('archived'),
    },
    ...Object.keys(notificationSettings.categories)
      .filter(category => category !== 'contacts') // Don't duplicate contacts tab
      .map((category) => ({
        key: category,
        label: t(category),
      })),
  ];

  return (
    <div className="notifications-page">
      <Card
        title={
          <Space>
            <BellOutlined />
            {t('notifications')}
            <Badge count={notifications.filter((n) => !n.read && !n.archived).length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                console.log('🔄 Manual refresh triggered');
                fetchNotifications();
                fetchUnreadCount();
              }}
            >
              Refresh
            </Button>
            <Button
              type="dashed"
              onClick={() => {
                console.log('🧪 Testing API directly...');
                const token = localStorage.getItem('token');
                fetch('http://localhost:5432/api/v1/notifications/test-contact', {
                  method: 'POST',
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                  }
                })
                .then(r => r.json())
                .then(data => {
                  console.log('✅ Test notification created:', data);
                  setTimeout(() => {
                    fetchNotifications();
                    fetchUnreadCount();
                  }, 1000);
                })
                .catch(err => console.error('❌ Error:', err));
              }}
            >
              Test Notification
            </Button>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={markAllAsRead}
              disabled={!notifications.some((n) => !n.read && !n.archived)}
            >
              {t('mark_all_read')}
            </Button>
            <Button icon={<SettingOutlined />} onClick={() => setShowSettings(!showSettings)}>
              {t('settings')}
            </Button>
            <Popconfirm
              title={t('clear_all_confirm')}
              onConfirm={clearAllNotifications}
              okText={t('yes')}
              cancelText={t('no')}
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                {t('clear_all')}
              </Button>
            </Popconfirm>
          </Space>
        }
      >
        {showSettings && (
          <div className="mb-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium mb-4">{t('notification_settings')}</h3>
            <Space direction="vertical" size="large" className="w-full">
              <div>
                <h4 className="font-medium mb-2">{t('notification_channels')}</h4>
                <Space direction="vertical">
                  <Switch
                    checked={notificationSettings.email}
                    onChange={(checked) => handleSettingsChange('email', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('email_notifications')}</span>
                </Space>
                <Space direction="vertical" className="ml-4">
                  <Switch
                    checked={notificationSettings.browser}
                    onChange={(checked) => handleSettingsChange('browser', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('browser_notifications')}</span>
                </Space>
                <Space direction="vertical" className="ml-4">
                  <Switch
                    checked={notificationSettings.sound}
                    onChange={(checked) => handleSettingsChange('sound', checked)}
                    checkedChildren={t('enabled')}
                    unCheckedChildren={t('disabled')}
                  />
                  <span>{t('sound_notifications')}</span>
                </Space>
              </div>
              <Divider />
              <div>
                <h4 className="font-medium mb-2">{t('notification_categories')}</h4>
                <Space direction="vertical">
                  {Object.entries(notificationSettings.categories).map(([category, enabled]) => (
                    <Space key={category}>
                      <Switch
                        checked={enabled}
                        onChange={(checked) => handleCategoryChange(category, checked)}
                        checkedChildren={t('enabled')}
                        unCheckedChildren={t('disabled')}
                      />
                      <span>{t(category)}</span>
                    </Space>
                  ))}
                </Space>
              </div>
            </Space>
          </div>
        )}

        <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />

        {/* Contact-specific header when contacts tab is active */}
        {activeTab === 'contacts' && (
          <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <Space>
              <MailOutlined className="text-blue-600" />
              <span className="font-medium text-blue-800">Contact Form Submissions</span>
              <Badge count={contactNotificationsCount} />
            </Space>
            <div className="mt-2 text-sm text-blue-600">
              Manage contact form submissions and notifications. Click "View Details" to see full contact information.
            </div>
          </div>
        )}

        {filteredNotifications.length === 0 ? (
          <Empty
            image={<InboxOutlined style={{ fontSize: 48 }} />}
            description={
              activeTab === 'contacts'
                ? "No contact form submissions yet"
                : t('no_notifications')
            }
          />
        ) : (
          <List
            itemLayout="horizontal"
            dataSource={filteredNotifications}
            renderItem={(notification) => (
              <List.Item
                actions={[
                  // Contact-specific view details button
                  notification.type === 'new_contact' && notification.data.contactId && (
                    <Button
                      type="link"
                      icon={<EyeOutlined />}
                      onClick={() => fetchContactDetails(notification.data.contactId)}
                    >
                      View Details
                    </Button>
                  ),
                  !notification.read && (
                    <Button type="link" onClick={() => markAsRead(notification.id)}>
                      {t('mark_read')}
                    </Button>
                  ),
                  <Button type="link" danger onClick={() => clearNotification(notification.id)}>
                    {t('delete')}
                  </Button>,
                  <Button type="link" onClick={() => archiveNotification(notification.id)}>
                    {t('archive')}
                  </Button>,
                ].filter(Boolean)}
              >
                <List.Item.Meta
                  avatar={getNotificationIcon(notification.type)}
                  title={
                    <Space>
                      <span>{notification.title || notification.message}</span>
                      {!notification.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                    </Space>
                  }
                  description={
                    <div>
                      <div>{notification.message}</div>
                      {notification.type === 'new_contact' && notification.data && (
                        <div className="mt-2 text-sm text-gray-500">
                          <Space>
                            <UserOutlined />
                            {notification.data.userName}
                            <MailOutlined />
                            {notification.data.userEmail}
                            {notification.data.phone && (
                              <>
                                <PhoneOutlined />
                                {notification.data.phone}
                              </>
                            )}
                          </Space>
                        </div>
                      )}
                      <div className="mt-1 text-xs text-gray-400">
                        <CalendarOutlined /> {new Date(notification.timestamp).toLocaleString()}
                      </div>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
        )}
      </Card>

      {/* Contact Details Modal */}
      <Modal
        title="Contact Details"
        open={showContactModal}
        onCancel={() => {
          setShowContactModal(false);
          setContactDetails(null);
        }}
        footer={[
          <Button key="close" onClick={() => setShowContactModal(false)}>
            Close
          </Button>,
          contactDetails && (
            <Popconfirm
              key="delete"
              title="Are you sure you want to delete this contact?"
              onConfirm={() => {
                deleteContact(contactDetails.C_Id);
                setShowContactModal(false);
                setContactDetails(null);
              }}
              okText="Yes"
              cancelText="No"
            >
              <Button type="primary" danger icon={<DeleteOutlined />}>
                Delete Contact
              </Button>
            </Popconfirm>
          ),
        ]}
        width={600}
      >
        {contactDetails && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Name">
              <Space>
                <UserOutlined />
                {contactDetails.user_name}
              </Space>
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              <Space>
                <MailOutlined />
                {contactDetails.user_email}
              </Space>
            </Descriptions.Item>
            {contactDetails.user_phone && (
              <Descriptions.Item label="Phone">
                <Space>
                  <PhoneOutlined />
                  {contactDetails.user_phone}
                </Space>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="Subject">
              {contactDetails.C_Title}
            </Descriptions.Item>
            <Descriptions.Item label="Message">
              <div className="whitespace-pre-wrap">
                {contactDetails.C_Body}
              </div>
            </Descriptions.Item>
            <Descriptions.Item label="Submitted">
              <Space>
                <CalendarOutlined />
                {new Date(contactDetails.created_at || new Date()).toLocaleString()}
              </Space>
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default NotificationsPage;
