# Complete Notification System Testing Guide

## 🚀 Quick Test Steps

### Step 1: Test Database and Backend
1. **Run the complete test script:**
   ```bash
   cd backend
   node scripts/complete-notification-test.js
   ```
   This will test the entire backend notification system.

### Step 2: Test API Endpoints
1. **Login as admin** at `http://localhost:3000/signin`
2. **Open browser console** (F12 → Console)
3. **Test the notification API:**
   ```javascript
   // Get your auth token
   const token = localStorage.getItem('token');
   console.log('Auth token:', token ? 'Found' : 'Missing');

   // Test creating a notification
   fetch('http://localhost:5432/api/v1/notifications/test-contact', {
     method: 'POST',
     headers: { 
       'Authorization': `Bearer ${token}`,
       'Content-Type': 'application/json'
     }
   })
   .then(r => r.json())
   .then(data => {
     console.log('Test notification created:', data);
     // Now fetch notifications to see if it appears
     return fetch('http://localhost:5432/api/v1/notifications', {
       headers: { 'Authorization': `<PERSON><PERSON> ${token}` }
     });
   })
   .then(r => r.json())
   .then(data => console.log('All notifications:', data))
   .catch(err => console.error('Error:', err));
   ```

### Step 3: Test Contact Form Integration
1. **Submit a contact form** at `http://localhost:3000/contact`
2. **Check backend console** for these messages:
   - `✅ Contact created successfully: [contact data]`
   - `✅ Notification created successfully: [notification data]`
3. **Check frontend console** on admin page for:
   - `📡 Fetching notifications from backend...`
   - `✅ Processed notifications: [array]`

### Step 4: Test Frontend Display
1. **Go to admin panel** `http://localhost:3000/admin`
2. **Check notification bell** in header for red badge
3. **Click notification bell** to see dropdown
4. **Visit notifications page** `http://localhost:3000/admin/notifications`
5. **Visit contact notifications** `http://localhost:3000/admin/notifications/contacts`

## 🔧 Troubleshooting

### Issue 1: No notifications in database
**Check:** Run `node scripts/complete-notification-test.js`
**Solution:** If test fails, check database connection and table structure

### Issue 2: Backend errors when creating notifications
**Check:** Backend console for error messages
**Common fixes:**
- Ensure NotificationModel is properly imported
- Check database table exists
- Verify database connection

### Issue 3: Frontend not showing notifications
**Check:** Browser console for API errors
**Common fixes:**
- Ensure you're logged in as admin
- Check auth token exists
- Verify API endpoints are accessible

### Issue 4: Contact form not creating notifications
**Check:** Backend console when submitting contact form
**Solution:** Ensure contact controller has NotificationModel import

## 🧪 Manual Testing Commands

### Test Database Connection:
```bash
cd backend
node scripts/debug-notifications.js
```

### Test Notification Creation:
```bash
cd backend
node scripts/test-notification-creation.js
```

### Test Complete System:
```bash
cd backend
node scripts/complete-notification-test.js
```

## 📋 Expected Results

### After submitting a contact form:
1. **Backend console shows:**
   ```
   ✅ Contact created successfully: { C_Id: 123, user_name: "...", ... }
   ✅ Notification created successfully: { id: 456, type: "new_contact", ... }
   ```

2. **Frontend console shows:**
   ```
   📡 Fetching notifications from backend...
   📡 Notifications API response: { success: true, data: [...] }
   ✅ Processed notifications: [{ id: 456, type: "new_contact", ... }]
   ```

3. **Admin header shows:**
   - Red badge with number on notification bell
   - Dropdown shows recent notifications

4. **Notification pages show:**
   - `/admin/notifications` - All notifications including contact ones
   - `/admin/notifications/contacts` - Contact-specific notifications

## 🎯 Key Files Modified

### Backend:
- `controllers/contactUsController.js` - Added notification creation
- `models/notificationModel.js` - Notification database operations
- `controllers/notificationController.js` - API endpoints
- `routes/notificationRoutes.js` - API routes
- `app.js` - Added notification routes

### Frontend:
- `contexts/NotificationContext.jsx` - Backend integration
- `layouts/PrivateLayout.jsx` - Header notification display
- `pages/notifications/NotificationsPage.jsx` - Main notifications page
- `pages/notifications/ContactNotificationsPage.jsx` - Contact notifications
- `pages/public/Contact.jsx` - Trigger notification refresh

### Database:
- `database/notifications_table.sql` - Notifications table structure

## 🚨 If Still Not Working

1. **Check backend logs** when submitting contact form
2. **Check frontend console** for API errors
3. **Run test scripts** to isolate the issue
4. **Verify database** has notifications table and data
5. **Check authentication** - ensure admin login works

The system should now work completely! If you're still having issues, please share:
1. Backend console output when submitting contact form
2. Frontend console output on admin page
3. Results from running the test scripts
