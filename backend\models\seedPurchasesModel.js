import db from "../config/db.js";

const SeedPurchasesModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Seed_Purchases (seed_id, seed_name, quantity, price_per_unit, total_amount, supplier, purchase_date, status, notes)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        data.seed_id || null,
        data.seed_name,
        data.quantity,
        data.price_per_unit,
        data.total_amount,
        data.supplier,
        data.purchase_date,
        data.status || 'Pending',
        data.notes || null,
      ],
    );

    const [newPurchase] = await db.query("SELECT * FROM Seed_Purchases WHERE id = ?", [
      result.insertId,
    ]);
    return newPurchase[0];
  },

  getAll: async () => {
    const [rows] = await db.execute("SELECT * FROM Seed_Purchases ORDER BY purchase_date DESC");
    return rows;
  },

  // Get all seed purchases with remaining quantities
  getAllWithRemainingQuantity: async () => {
    const [rows] = await db.execute(`
      SELECT
        sp.*,
        COALESCE(SUM(st.quantity), 0) as transferred_quantity,
        (sp.quantity - COALESCE(SUM(st.quantity), 0)) as remaining_quantity
      FROM Seed_Purchases sp
      LEFT JOIN Seed_Transfers st ON sp.id = st.seed_id AND st.status IN ('Completed', 'Pending', 'In Transit')
      GROUP BY sp.id
      ORDER BY sp.purchase_date DESC
    `);
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute("SELECT * FROM Seed_Purchases WHERE id = ?", [id]);
    return rows[0];
  },

  getBySeedId: async (seedId) => {
    const [rows] = await db.execute("SELECT * FROM Seed_Purchases WHERE seed_id = ? ORDER BY purchase_date DESC", [seedId]);
    return rows;
  },

  update: async (id, data) => {
    const [result] = await db.execute(
      `UPDATE Seed_Purchases SET seed_id = ?, seed_name = ?, quantity = ?, price_per_unit = ?, total_amount = ?, supplier = ?, purchase_date = ?, status = ?, notes = ?
       WHERE id = ?`,
      [
        data.seed_id,
        data.seed_name,
        data.quantity,
        data.price_per_unit,
        data.total_amount,
        data.supplier,
        data.purchase_date,
        data.status,
        data.notes,
        id,
      ],
    );
    return result.affectedRows > 0 ? { id, ...data } : null;
  },

  delete: async (id) => {
    const [result] = await db.execute("DELETE FROM Seed_Purchases WHERE id = ?", [id]);
    return result;
  },

  // Get total quantity purchased for a specific seed
  getTotalPurchasedQuantity: async (seedId) => {
    const [rows] = await db.execute(
      "SELECT SUM(quantity) as total_quantity FROM Seed_Purchases WHERE seed_id = ? AND status = 'Completed'",
      [seedId]
    );
    return rows[0]?.total_quantity || 0;
  },

  // Get purchase statistics
  getStatistics: async () => {
    const [rows] = await db.execute(`
      SELECT 
        COUNT(*) as total_purchases,
        SUM(quantity) as total_quantity,
        SUM(total_amount) as total_amount,
        COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_purchases,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_purchases
      FROM Seed_Purchases
    `);
    return rows[0];
  },
};

export default SeedPurchasesModel;
