import db from "../config/db.js";

const ContactReadStatusModel = {
  // Mark a contact as read by a specific user
  markAsRead: async (contactId, userId) => {
    try {
      const [result] = await db.execute(
        `INSERT INTO ContactReadStatus (contact_id, user_id) 
         VALUES (?, ?) 
         ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP`,
        [contactId, userId]
      );
      return result;
    } catch (error) {
      console.error('Error marking contact as read:', error);
      throw error;
    }
  },

  // Mark all contacts as read by a specific user
  markAllAsRead: async (userId) => {
    try {
      const [result] = await db.execute(
        `INSERT INTO ContactReadStatus (contact_id, user_id)
         SELECT C_Id, ? FROM ContactUs
         ON DUPLICATE KEY UPDATE read_at = CURRENT_TIMESTAMP`,
        [userId]
      );
      return result;
    } catch (error) {
      console.error('Error marking all contacts as read:', error);
      throw error;
    }
  },

  // Get read status for all contacts for a specific user
  getReadStatusForUser: async (userId) => {
    try {
      const [rows] = await db.execute(
        `SELECT contact_id FROM ContactReadStatus WHERE user_id = ?`,
        [userId]
      );
      return rows.map(row => row.contact_id);
    } catch (error) {
      console.error('Error getting read status:', error);
      throw error;
    }
  },

  // Check if a specific contact is read by a specific user
  isContactRead: async (contactId, userId) => {
    try {
      const [rows] = await db.execute(
        `SELECT id FROM ContactReadStatus WHERE contact_id = ? AND user_id = ?`,
        [contactId, userId]
      );
      return rows.length > 0;
    } catch (error) {
      console.error('Error checking contact read status:', error);
      throw error;
    }
  },

  // Get contacts with their read status for a specific user
  getContactsWithReadStatus: async (userId) => {
    try {
      const [rows] = await db.execute(
        `SELECT c.*, 
                CASE WHEN crs.contact_id IS NOT NULL THEN true ELSE false END as is_read,
                crs.read_at
         FROM ContactUs c
         LEFT JOIN ContactReadStatus crs ON c.C_Id = crs.contact_id AND crs.user_id = ?
         ORDER BY c.C_Id DESC`,
        [userId]
      );
      return rows;
    } catch (error) {
      console.error('Error getting contacts with read status:', error);
      throw error;
    }
  },

  // Remove read status for a contact (when contact is deleted)
  removeReadStatus: async (contactId) => {
    try {
      const [result] = await db.execute(
        `DELETE FROM ContactReadStatus WHERE contact_id = ?`,
        [contactId]
      );
      return result;
    } catch (error) {
      console.error('Error removing read status:', error);
      throw error;
    }
  },

  // Clear all read status for a user
  clearAllReadStatus: async (userId) => {
    try {
      const [result] = await db.execute(
        `DELETE FROM ContactReadStatus WHERE user_id = ?`,
        [userId]
      );
      return result;
    } catch (error) {
      console.error('Error clearing all read status:', error);
      throw error;
    }
  },
};

export default ContactReadStatusModel;
