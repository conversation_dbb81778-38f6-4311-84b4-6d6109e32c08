import ContactUsModel from '../models/contactUsModel.js';
import NotificationModel from '../models/notificationModel.js';

async function testContactNotificationFlow() {
  try {
    console.log('🧪 Testing Complete Contact → Notification Flow\n');

    // Step 1: Create a test contact (simulating form submission)
    console.log('1. Creating test contact...');
    const testContactData = {
      C_Title: 'Test Contact for Notification Flow',
      C_Body: 'This is a test contact to verify the complete notification flow works.',
      user_name: 'Test User',
      user_email: '<EMAIL>',
      user_phone: '0701234567'
    };

    const contact = await ContactUsModel.create(testContactData);
    console.log('✅ Contact created:', contact);

    // Step 2: Create notification for the contact (simulating controller logic)
    console.log('\n2. Creating notification for contact...');
    const notification = await NotificationModel.createContactNotification(contact);
    console.log('✅ Notification created:', notification);

    // Step 3: Verify notification exists in database
    console.log('\n3. Verifying notification in database...');
    const allNotifications = await NotificationModel.getAll();
    console.log(`✅ Total notifications in database: ${allNotifications.length}`);
    
    const contactNotifications = await NotificationModel.getByCategory('contacts');
    console.log(`✅ Contact notifications: ${contactNotifications.length}`);

    if (contactNotifications.length > 0) {
      console.log('\n📋 Recent contact notifications:');
      contactNotifications.slice(0, 3).forEach((notif, index) => {
        console.log(`  ${index + 1}. ${notif.title} - ${notif.message}`);
        console.log(`     Contact: ${notif.user_name} (${notif.user_email})`);
        console.log(`     Created: ${notif.created_at}`);
        console.log(`     Read: ${notif.read_status ? 'Yes' : 'No'}`);
      });
    }

    // Step 4: Test unread count
    console.log('\n4. Testing unread count...');
    const unreadCount = await NotificationModel.getUnreadCount();
    console.log(`✅ Unread notifications: ${unreadCount}`);

    // Step 5: Clean up test data
    console.log('\n5. Cleaning up test data...');
    await NotificationModel.delete(notification.id);
    await ContactUsModel.delete(contact.C_Id);
    console.log('✅ Test data cleaned up');

    console.log('\n🎉 Contact notification flow test completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   - Contact created with ID: ${contact.C_Id}`);
    console.log(`   - Notification created with ID: ${notification.id}`);
    console.log(`   - Notification category: ${notification.category}`);
    console.log(`   - Notification type: ${notification.type}`);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    if (error.stack) {
      console.error('Stack trace:', error.stack);
    }
  }
}

testContactNotificationFlow();
