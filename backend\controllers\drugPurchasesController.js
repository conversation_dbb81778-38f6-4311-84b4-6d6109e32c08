import DrugPurchasesModel from "../models/drugPurchasesModel.js";
import <PERSON><PERSON> from "joi";

// Validation schema
const drugPurchaseSchema = Joi.object({
  drug_id: Joi.number().integer().optional().allow(null),
  drug_name: Joi.string().min(3).max(100).required(),
  quantity: Joi.number().integer().min(1).required(),
  price_per_unit: Joi.number().min(0).required(),
  total_amount: Joi.number().min(0).required(),
  supplier: Joi.string().min(3).max(100).required(),
  purchase_date: Joi.date().required(),
  status: Joi.string().valid('Pending', 'Completed', 'Cancelled').optional(),
  notes: Joi.string().max(500).optional().allow('', null),
});

const DrugPurchasesController = {
  // Get all drug purchases with remaining quantities
  getAll: async (req, res) => {
    try {
      const purchases = await DrugPurchasesModel.getAllWithRemainingQuantity();
      res.status(200).json({
        success: true,
        total: purchases.length,
        data: purchases,
      });
    } catch (error) {
      console.error("Error fetching drug purchases:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch drug purchases",
      });
    }
  },

  // Get drug purchase by ID
  getById: async (req, res) => {
    try {
      const { id } = req.params;
      const purchase = await DrugPurchasesModel.getById(id);

      if (!purchase) {
        return res.status(404).json({
          success: false,
          error: "Drug purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        data: purchase,
      });
    } catch (error) {
      console.error("Error fetching drug purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch drug purchase",
      });
    }
  },

  // Create new drug purchase
  create: async (req, res) => {
    try {
      const { error, value } = drugPurchaseSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
      }

      const newPurchase = await DrugPurchasesModel.create(value);
      res.status(201).json({
        success: true,
        data: newPurchase,
      });
    } catch (error) {
      console.error("Error creating drug purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to create drug purchase",
      });
    }
  },

  // Update drug purchase
  update: async (req, res) => {
    try {
      const { id } = req.params;
      const { error, value } = drugPurchaseSchema.validate(req.body);

      if (error) {
        return res.status(400).json({
          success: false,
          error: error.details[0].message,
        });
      }

      const updatedPurchase = await DrugPurchasesModel.update(id, value);

      if (!updatedPurchase) {
        return res.status(404).json({
          success: false,
          error: "Drug purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        data: updatedPurchase,
      });
    } catch (error) {
      console.error("Error updating drug purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to update drug purchase",
      });
    }
  },

  // Delete drug purchase
  delete: async (req, res) => {
    try {
      const { id } = req.params;
      const result = await DrugPurchasesModel.delete(id);

      if (result.affectedRows === 0) {
        return res.status(404).json({
          success: false,
          error: "Drug purchase not found",
        });
      }

      res.status(200).json({
        success: true,
        message: "Drug purchase deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting drug purchase:", error);
      res.status(500).json({
        success: false,
        error: "Failed to delete drug purchase",
      });
    }
  },

  // Get purchases by drug ID
  getByDrugId: async (req, res) => {
    try {
      const { drugId } = req.params;
      const purchases = await DrugPurchasesModel.getByDrugId(drugId);
      
      res.status(200).json({
        success: true,
        total: purchases.length,
        data: purchases,
      });
    } catch (error) {
      console.error("Error fetching drug purchases by drug ID:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch drug purchases",
      });
    }
  },

  // Get purchase statistics
  getStatistics: async (req, res) => {
    try {
      const stats = await DrugPurchasesModel.getStatistics();
      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error) {
      console.error("Error fetching purchase statistics:", error);
      res.status(500).json({
        success: false,
        error: "Failed to fetch statistics",
      });
    }
  },
};

export default DrugPurchasesController;
