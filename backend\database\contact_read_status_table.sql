-- Create ContactReadStatus table to track which contacts have been read by which admin users
CREATE TABLE IF NOT EXISTS ContactReadStatus (
    id INT AUTO_INCREMENT PRIMARY KEY,
    contact_id INT NOT NULL,
    user_id INT NOT NULL,
    read_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    FOREIGN KEY (contact_id) REFERENCES ContactUs(C_Id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(u_Id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate entries
    UNIQUE KEY unique_contact_user (contact_id, user_id),
    
    -- Indexes for better performance
    INDEX idx_contact_id (contact_id),
    INDEX idx_user_id (user_id),
    INDEX idx_read_at (read_at)
);

-- Optional: Add a read_status column to ContactUs table for quick reference
-- ALTER TABLE ContactUs ADD COLUMN read_status BOOLEAN DEFAULT FALSE;
