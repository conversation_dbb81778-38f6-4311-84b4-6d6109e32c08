import db from "../config/db.js";

const NotificationModel = {
  create: async (data) => {
    const [result] = await db.execute(
      `INSERT INTO Notifications (type, title, message, category, contact_id, user_id, data, read_status, created_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        data.type,
        data.title,
        data.message,
        data.category || 'system',
        data.contact_id || null,
        data.user_id || null,
        JSON.stringify(data.data || {}),
        data.read_status || false,
      ]
    );

    const [newNotification] = await db.execute(
      `SELECT * FROM Notifications WHERE id = ?`,
      [result.insertId]
    );

    return newNotification[0];
  },

  getAll: async () => {
    const [rows] = await db.execute(
      `SELECT n.*, c.user_name, c.user_email, c.C_Title as contact_title 
       FROM Notifications n 
       LEFT JOIN ContactUs c ON n.contact_id = c.C_Id 
       ORDER BY n.created_at DESC`
    );
    return rows;
  },

  getById: async (id) => {
    const [rows] = await db.execute(
      `SELECT n.*, c.user_name, c.user_email, c.C_Title as contact_title 
       FROM Notifications n 
       LEFT JOIN ContactUs c ON n.contact_id = c.C_Id 
       WHERE n.id = ?`,
      [id]
    );
    return rows[0];
  },

  getByCategory: async (category) => {
    const [rows] = await db.execute(
      `SELECT n.*, c.user_name, c.user_email, c.C_Title as contact_title 
       FROM Notifications n 
       LEFT JOIN ContactUs c ON n.contact_id = c.C_Id 
       WHERE n.category = ? 
       ORDER BY n.created_at DESC`,
      [category]
    );
    return rows;
  },

  getUnreadCount: async () => {
    const [rows] = await db.execute(
      `SELECT COUNT(*) as count FROM Notifications WHERE read_status = false`
    );
    return rows[0].count;
  },

  markAsRead: async (id) => {
    const [result] = await db.execute(
      `UPDATE Notifications SET read_status = true WHERE id = ?`,
      [id]
    );
    return result.affectedRows > 0;
  },

  markAllAsRead: async () => {
    const [result] = await db.execute(
      `UPDATE Notifications SET read_status = true WHERE read_status = false`
    );
    return result.affectedRows;
  },

  delete: async (id) => {
    const [result] = await db.execute(
      `DELETE FROM Notifications WHERE id = ?`,
      [id]
    );
    return result.affectedRows > 0;
  },

  deleteAll: async () => {
    const [result] = await db.execute(`DELETE FROM Notifications`);
    return result.affectedRows;
  },

  // Contact-specific methods
  createContactNotification: async (contactData) => {
    return await NotificationModel.create({
      type: 'new_contact',
      title: 'New Contact Form Submission',
      message: `New contact from ${contactData.user_name}: ${contactData.C_Title}`,
      category: 'contacts',
      contact_id: contactData.C_Id,
      data: {
        contact_id: contactData.C_Id,
        user_name: contactData.user_name,
        user_email: contactData.user_email,
        subject: contactData.C_Title,
        message: contactData.C_Body,
        phone: contactData.user_phone,
      },
    });
  },
};

export default NotificationModel;
