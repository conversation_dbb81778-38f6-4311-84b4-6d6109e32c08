import { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Save, X } from 'lucide-react';
import { useLanguage } from '../../contexts/LanguageContext';
import Button from '../../components/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/feed-components/card';

const EditDrugTransferPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { language, translations } = useLanguage();

  const [formData, setFormData] = useState({
    drug_name: '',
    quantity: '',
    farm_name: '',
    farm_location: '',
    transfer_date: '',
    transferred_by: '',
    notes: '',
    status: '',
  });
  const [loading, setLoading] = useState(true);
  const [errors, setErrors] = useState({});
  const [submitError, setSubmitError] = useState(null);
  const [fetchError, setFetchError] = useState(null);
  const [transfers, setTransfers] = useState([]);

  const t = (key) => {
    if (!translations || !translations[language]) return key;
    return translations[language][key] || key;
  };

  useEffect(() => {
    const fetchTransfer = async () => {
      setLoading(true);
      setFetchError(null);
      try {
        const res = await fetch(`http://localhost:5432/api/v1/drug-transfers/${id}`);
        if (!res.ok) throw new Error('Failed to fetch transfer');
        const json = await res.json();
        if (!json.success || !json.data) throw new Error('Transfer not found');
        setFormData({
          drug_name: json.data.drug_name || '',
          quantity: json.data.quantity?.toString() || '',
          farm_name: json.data.farm_name || '',
          farm_location: json.data.farm_location || '',
          transfer_date: json.data.transfer_date ? json.data.transfer_date.slice(0, 10) : '',
          transferred_by: json.data.transferred_by || '',
          notes: json.data.notes || '',
          status: json.data.status || '',
        });
      } catch (err) {
        setFetchError(err.message);
      } finally {
        setLoading(false);
      }
    };
    fetchTransfer();
  }, [id]);

  useEffect(() => {
    fetch('http://localhost:5432/api/v1/drug-transfers')
      .then(res => res.json())
      .then(json => setTransfers(json.data || []));
  }, []);

  const allDrugsFromTransfers = transfers;

  const validateForm = () => {
    const newErrors = {};
    if (!formData.drug_name.trim()) newErrors.drug_name = 'Drug name is required';
    if (!formData.quantity || parseInt(formData.quantity) < 1) newErrors.quantity = 'Quantity must be at least 1';
    if (!formData.farm_name.trim()) newErrors.farm_name = 'Farm name is required';
    if (!formData.transfer_date) newErrors.transfer_date = 'Transfer date is required';
    if (!formData.transferred_by.trim()) newErrors.transferred_by = 'Transferred by is required';
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) setErrors(prev => ({ ...prev, [name]: '' }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitError(null);
    if (!validateForm()) return;
    try {
      setLoading(true);
      const transferData = {
        ...formData,
        quantity: parseInt(formData.quantity),
        status: formData.status || 'Pending',
      };
      const response = await fetch(`http://localhost:5432/api/v1/drug-transfers/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(transferData),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update transfer');
      }
      await response.json();
      alert('Transfer updated successfully!');
      navigate('/admin/drugs/transfers');
    } catch (error) {
      setSubmitError(error.message || 'Failed to update transfer. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="p-8 text-center text-lg">Loading...</div>;
  }
  if (fetchError) {
    return <div className="p-8 text-center text-red-600">{fetchError}</div>;
  }

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 p-4 sm:p-6 lg:p-8 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
      <div className="max-w-4xl mx-auto space-y-6">
        <div className={`flex items-center gap-4 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
          <Button
            variant="secondary"
            size="sm"
            onClick={() => navigate('/admin/drugs/transfers')}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {t('back')}
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Edit Drug Transfer</h1>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Update drug transfer information</p>
          </div>
        </div>
        <Card>
          <CardHeader>
            <CardTitle>Edit Transfer Information</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Drug Name *</label>
                  <select
                    name="drug_name"
                    value={formData.drug_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.drug_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  >
                    <option value="">Select a drug</option>
                    {allDrugsFromTransfers.map((transfer, idx) => (
                      <option key={transfer.id || idx} value={transfer.drug_name}>
                        {transfer.drug_name}
                      </option>
                    ))}
                  </select>
                  {errors.drug_name && <p className="text-red-500 text-sm mt-1">{errors.drug_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Quantity *</label>
                  <input
                    type="number"
                    name="quantity"
                    value={formData.quantity}
                    onChange={handleChange}
                    min="1"
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.quantity ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter quantity"
                  />
                  {errors.quantity && <p className="text-red-500 text-sm mt-1">{errors.quantity}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm Name *</label>
                  <input
                    type="text"
                    name="farm_name"
                    value={formData.farm_name}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.farm_name ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter farm name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.farm_name && <p className="text-red-500 text-sm mt-1">{errors.farm_name}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Farm Location</label>
                  <input
                    type="text"
                    name="farm_location"
                    value={formData.farm_location}
                    onChange={handleChange}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                    placeholder="Enter farm location"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transfer Date *</label>
                  <input
                    type="date"
                    name="transfer_date"
                    value={formData.transfer_date}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.transfer_date ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                  />
                  {errors.transfer_date && <p className="text-red-500 text-sm mt-1">{errors.transfer_date}</p>}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Transferred By *</label>
                  <input
                    type="text"
                    name="transferred_by"
                    value={formData.transferred_by}
                    onChange={handleChange}
                    className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white ${errors.transferred_by ? 'border-red-500' : 'border-gray-300 dark:border-gray-600'}`}
                    placeholder="Enter person name"
                    dir={language === 'ps' ? 'rtl' : 'ltr'}
                  />
                  {errors.transferred_by && <p className="text-red-500 text-sm mt-1">{errors.transferred_by}</p>}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Notes</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-[#FF6B00] dark:bg-gray-700 dark:text-white"
                  placeholder="Enter any additional notes"
                  dir={language === 'ps' ? 'rtl' : 'ltr'}
                />
              </div>
              {submitError && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <p className="text-red-800 text-sm">{submitError}</p>
                </div>
              )}
              <div className={`flex gap-4 pt-6 ${language === 'ps' ? 'flex-row-reverse' : ''}`}>
                <Button type="submit" className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Save Changes
                </Button>
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => navigate('/admin/drugs/transfers')}
                  className="flex items-center gap-2"
                >
                  <X className="h-4 w-4" />
                  {t('cancel')}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default EditDrugTransferPage; 