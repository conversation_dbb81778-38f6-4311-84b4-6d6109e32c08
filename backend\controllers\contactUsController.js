import <PERSON><PERSON> from "joi";
import async<PERSON>and<PERSON> from "../middlewares/asyncHandler.js";
import ContactUsModel from "../models/contactUsModel.js";

const contactSchema = Joi.object({
  C_Title: Joi.string().min(3).max(40).required(),
  C_Body: Joi.string().min(10).required(),
  user_name: Joi.string().min(3).max(70).required(),
  user_email: Joi.string().email().required(),
  user_phone: Joi.string()
    .pattern(/^((\+93)|0)?7[0-9]{8}$/)
    .optional()
    .allow(null, ""),
});

const ContactUsController = {
  create: asyncHandler(async (req, res) => {
    const { error, value } = contactSchema.validate(req.body, {
      abortEarly: false,
    });
    if (error) {
      // collect all messages
      const errors = error.details.map((d) => d.message);
      return res.status(400).json({ success: false, errors });
    }

    const contact = await ContactUsModel.create(value);

    // Create notification for admin
    try {
      await NotificationModel.createContactNotification(contact);
    } catch (notificationError) {
      console.error("Failed to create notification:", notificationError);
      // Don't fail the contact creation if notification fails
    }

    res.status(201).json({
      success: true,
      message: "Contact message submitted",
      data: contact,
    });
  }),

  getAll: asyncHandler(async (req, res) => {
    const messages = await ContactUsModel.getAll();
    res.json({
      success: true,
      totalContact: messages.length,
      data: messages,
    });
  }),

  getById: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const message = await ContactUsModel.getById(id);
    if (!message) {
      return res
        .status(404)
        .json({ success: false, message: "Message not found" });
    }
    res.json({ success: true, data: message });
  }),

  update: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const existing = await ContactUsModel.getById(id);
    if (!existing) {
      return res
        .status(404)
        .json({ success: false, message: "Message not found" });
    }

    const { error, value } = contactSchema.validate(req.body, {
      abortEarly: false,
    });
    if (error) {
      const errors = error.details.map((d) => d.message);
      return res.status(400).json({ success: false, errors });
    }

    const updated = await ContactUsModel.update(id, value);
    res.json({
      success: true,
      message: "Contact message updated successfully",
      data: updated,
    });
  }),

  delete: asyncHandler(async (req, res) => {
    const id = req.params.id;
    const result = await ContactUsModel.delete(id);
    if (result.affectedRows === 0) {
      return res
        .status(404)
        .json({ success: false, message: "Message not found" });
    }
    res.json({ success: true, message: "Message deleted successfully" });
  }),
};

export default ContactUsController;
