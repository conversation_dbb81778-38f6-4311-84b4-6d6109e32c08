"use client"

import { useState, useEffect } from "react"
import { useNavigate } from "react-router-dom"
import {
  Search,
  Bell,
  ChevronDown,
  User,
  LogOut,
  Moon,
  Sun,
  MenuIcon,
  Calendar,
  HelpCircle,
} from "lucide-react"
import { Badge as BadgeComponent } from "@/components/management-system/ui/Badge"
import { Avatar as AvatarComponent, AvatarImage, AvatarFallback } from "@/components/management-system/ui/Avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/management-system/ui/Dropdown-menu"
import SideBar from "../components/admin/sidebar"
import { useAuth } from "../contexts/AuthContext"
import { useLanguage } from "../contexts/LanguageContext"
import { useUser } from "../contexts/UsersContext"
import { useNotifications } from "../contexts/NotificationContext"
import axios from "axios"

export default function AdminLayout({ children }) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")

  const [isMobile, setIsMobile] = useState(false)
  const [darkMode, setDarkMode] = useState(false)
  const [currentDate, setCurrentDate] = useState(new Date())

  // Contact notifications state
  const [contactNotifications, setContactNotifications] = useState([])
  const [readContacts, setReadContacts] = useState(() => {
    try {
      const saved = localStorage.getItem('readContacts');
      return saved ? new Set(JSON.parse(saved)) : new Set();
    } catch (error) {
      return new Set();
    }
  })

  // Search functionality
  const [searchResults, setSearchResults] = useState([])
  const [isSearching, setIsSearching] = useState(false)
  const [showSearchResults, setShowSearchResults] = useState(false)

  // Mock data for demonstration - replace with your actual data sources
  const searchableContent = [
    { id: 1, title: "Dashboard", type: "page", url: "/admin/dashboard" },
    { id: 2, title: "Users Management", type: "page", url: "/admin/users" },
    { id: 3, title: "Settings", type: "page", url: "/admin/settings" },
    { id: 4, title: "Reports", type: "page", url: "/admin/reports" },
    { id: 5, title: "Analytics", type: "page", url: "/admin/analytics" },
    { id: 6, title: "Profile Settings", type: "setting", url: "/admin/profile" },
    { id: 7, title: "System Configuration", type: "setting", url: "/admin/setting" },
    { id: 8, title: "User management", type: "permission", url: "/admin/users" },
  ]

  const navigate = useNavigate()
  const { user, logout } = useAuth()
  const { language = "en", toggleLanguage, t } = useLanguage() || {}
  const { userData } = useUser()
  const { notifications, unreadCount, markAsRead } = useNotifications()

  // Fetch contact notifications
  const fetchContactNotifications = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) return;

      const response = await axios.get('http://localhost:5432/api/v1/contact-us', {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (response.data.success) {
        // Get latest 5 contacts for header display
        const latestContacts = response.data.data.slice(0, 5);
        setContactNotifications(latestContacts);
      }
    } catch (error) {
      console.error('Error fetching contact notifications:', error);
    }
  };

  // Mark contact as read
  const markContactAsRead = (contactId) => {
    setReadContacts(prev => {
      const newSet = new Set([...prev, contactId]);
      localStorage.setItem('readContacts', JSON.stringify([...newSet]));
      return newSet;
    });
  };

  // Get unread contact count
  const getUnreadContactCount = () => {
    return contactNotifications.filter(contact => !readContacts.has(contact.C_Id)).length;
  };

  // Check mobile and set sidebar state
  useEffect(() => {
    const checkIfMobile = () => {
      const mobile = window.innerWidth < 768
      setIsMobile(mobile)
      // Always show sidebar on desktop, hide on mobile by default
      if (!mobile) {
        setIsSidebarOpen(true)
      } else {
        setIsSidebarOpen(false)
      }
    }
    checkIfMobile()
    window.addEventListener("resize", checkIfMobile)
    return () => window.removeEventListener("resize", checkIfMobile)
  }, [])

  // Set direction and language
  useEffect(() => {
    document.documentElement.dir = language === "ps" ? "rtl" : "ltr"
    document.documentElement.lang = language
  }, [language])

  // Handle dark mode class on html element
  useEffect(() => {
    if (darkMode) {
      document.documentElement.classList.add("dark")
    } else {
      document.documentElement.classList.remove("dark")
    }
  }, [darkMode])

  // Update current date
  useEffect(() => {
    const timer = setInterval(() => setCurrentDate(new Date()), 60000)
    return () => clearInterval(timer)
  }, [])

  // Fetch contact notifications on mount and periodically
  useEffect(() => {
    fetchContactNotifications();
    const interval = setInterval(fetchContactNotifications, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [])

  const handleSearch = (e) => {
    const query = e.target.value
    setSearchQuery(query)

    if (query.trim() === "") {
      setSearchResults([])
      setShowSearchResults(false)
      setIsSearching(false)
      return
    }

    setIsSearching(true)

    // Simulate search delay (remove in production if using real-time search)
    setTimeout(() => {
      const results = searchableContent.filter(
        (item) =>
          item.title.toLowerCase().includes(query.toLowerCase()) ||
          item.type.toLowerCase().includes(query.toLowerCase()),
      )

      setSearchResults(results)
      setShowSearchResults(true)
      setIsSearching(false)
    }, 300)
  }

  const handleSearchResultClick = (result) => {
    navigate(result.url)
    setSearchQuery("")
    setShowSearchResults(false)
    setSearchResults([])
  }

  const handleSearchBlur = () => {
    // Delay hiding results to allow clicking on them
    setTimeout(() => {
      setShowSearchResults(false)
    }, 200)
  }

  const handleNotificationClick = (n) => {
    if (!n.read) {
      markAsRead(n.id);
    }
    navigate('/admin/notifications');
  }
  const handleProfileClick = () => navigate("/admin/profile")
  const handleLogout = () => {
    logout()
    navigate("/signin")
  }

  const rtlClasses = {
    sidebar: language === "ps" ? "right-0" : "left-0",
    content: language === "ps" ? "mr-64" : "ml-64",
    contentCollapsed: language === "ps" ? "" : "",
    searchIcon: language === "ps" ? "right-3" : "left-3",
    searchInput: language === "ps" ? "pr-10 pl-4" : "pl-10 pr-4",
    dropdownItem: language === "ps" ? "flex-row-reverse" : "flex-row",
    dropdownIcon: language === "ps" ? "ml-2" : "mr-2",
  }

  // Debug logging removed

  const getUserInitials = () => {
    if (userData?.firstName && userData?.lastName) {
      return `${userData.firstName[0]}${userData.lastName[0]}`.toUpperCase()
    } else if (userData?.firstName) {
      return userData.firstName[0].toUpperCase()
    }
    return ""
  }

  // Format date based on language
  const formatDate = () => {
    const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
    return currentDate.toLocaleDateString(language === "ps" ? "ps-AF" : "en-US", options)
  }

  // Toggle sidebar function
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen)
  }

  // Close search results when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest(".relative")) {
        setShowSearchResults(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white`}>
      <div className="flex">
        {/* Mobile Sidebar Overlay */}
        {isMobile && isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black/50 z-40 backdrop-blur-sm"
            onClick={() => setIsSidebarOpen(false)}
            aria-hidden="true"
          ></div>
        )}

        {/* Sidebar */}
        <div
          className={`fixed top-0 h-screen z-50 transition-all duration-300 ease-in-out ${
            isSidebarOpen ? "translate-x-0" : language === "ps" ? "translate-x-full" : "-translate-x-full"
          } ${rtlClasses.sidebar} w-64`}
        >
          <SideBar isOpen={true} onToggle={setIsSidebarOpen} darkMode={darkMode} />
        </div>

        {/* Content */}
        <div
          className={`flex-1 transition-all duration-300 ${
            !isMobile && isSidebarOpen
              ? rtlClasses.content
              : !isMobile && !isSidebarOpen
                ? rtlClasses.contentCollapsed
                : "ml-0 mr-0"
          }`}
        >
          {/* Header */}
          <header className="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-30">
            <div className="px-4 sm:px-6 lg:px-8">
              <div className="flex items-center gap-2 justify-between h-16">
                {/* Left side with menu toggle and breadcrumb */}
                <div className="flex items-center gap-4">
                  {/* Menu Toggle Button */}
                  <button
                    onClick={toggleSidebar}
                    className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/30"
                    aria-label="Toggle sidebar"
                  >
                    <MenuIcon size={20} />
                  </button>

                  {/* Date Display */}
                  <div
                    className="hidden md:flex items-center  text-sm text-gray-500 dark:text-gray-400"
                    dir={language === "ps" ? "rtl" : "ltr"}
                  >
                    <Calendar size={16} className={language === "ps" ? "ml-2" : "mr-2"} />
                    <span>{formatDate()}</span>
                  </div>
                </div>

                {/* Search Bar - Center */}
                <div className="flex-1 max-w-lg flex gap-2 mx-auto relative">
                  <div className="relative w-full">
                    <Search
                      className={`absolute ${language === "ps" ? "right-3" : "left-3"} top-1/2 transform -translate-y-1/2 text-gray-400`}
                      size={18}
                    />
                    <input
                      type="text"
                      placeholder={language === "ps" ? "لټون..." : "Search..."}
                      value={searchQuery}
                      onChange={handleSearch}
                      onBlur={handleSearchBlur}
                      onFocus={() => searchQuery && setShowSearchResults(true)}
                      className={`w-full ${language === "ps" ? "pr-10 pl-4" : "pl-10 pr-4"} py-2 border border-gray-200 dark:border-gray-700 dark:bg-gray-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 bg-gray-50 dark:bg-gray-700 text-sm`}
                      dir={language === "ps" ? "rtl" : "ltr"}
                    />

                    {/* Search Results Dropdown */}
                    {showSearchResults && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto">
                        {isSearching ? (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary mx-auto"></div>
                            <span className="mt-2 block text-sm">
                              {language === "ps" ? "لټول کیږي..." : "Searching..."}
                            </span>
                          </div>
                        ) : searchResults.length > 0 ? (
                          <div className="py-2">
                            <div className="px-3 py-2 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider border-b border-gray-100 dark:border-gray-700">
                              {language === "ps" ? "پایلې" : "Results"} ({searchResults.length})
                            </div>
                            {searchResults.map((result) => (
                              <button
                                key={result.id}
                                onClick={() => handleSearchResultClick(result)}
                                className="w-full px-3 py-2 text-left hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-between group"
                                dir={language === "ps" ? "rtl" : "ltr"}
                              >
                                <div className="flex items-center">
                                  <div
                                    className={`w-2 h-2 rounded-full mr-3 ${
                                      result.type === "page"
                                        ? "bg-blue-500"
                                        : result.type === "setting"
                                          ? "bg-green-500"
                                          : result.type === "permission"
                                            ? "bg-yellow-500"
                                            : result.type === "tool"
                                              ? "bg-purple-500"
                                              : "bg-gray-500"
                                    }`}
                                  ></div>
                                  <div>
                                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                                      {result.title}
                                    </div>
                                    <div className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                                      {result.type}
                                    </div>
                                  </div>
                                </div>
                                <ChevronDown
                                  size={14}
                                  className={`text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transform ${
                                    language === "ps" ? "rotate-90" : "-rotate-90"
                                  }`}
                                />
                              </button>
                            ))}
                          </div>
                        ) : (
                          <div className="p-4 text-center text-gray-500 dark:text-gray-400">
                            <Search size={24} className="mx-auto mb-2 opacity-50" />
                            <span className="text-sm">
                              {language === "ps" ? "هیڅ پایله ونه موندل شوه" : "No results found"}
                            </span>
                            <span className="text-xs block mt-1">
                              {language === "ps" ? "بل کلیدي ټکي وآزمویئ" : "Try different keywords"}
                            </span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Right Side */}
                <div className="flex items-center space-x-3">
                  {/* Help Button */}
                  <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                    <HelpCircle size={18} />
                  </button>

                  {/* Language Selector */}
                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex items-center space-x-1 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                      <span className="text-sm font-medium">{language === "en" ? "EN" : "PS"}</span>
                      <ChevronDown size={14} />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-40" sideOffset={5} dir={language === "ps" ? "rtl" : "ltr"}>
                      <DropdownMenuLabel className="text-center">
                        {language === "ps" ? "ژبه وټاکئ" : "Select Language"}
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => language !== "en" && toggleLanguage()}
                        className={`${language === "en" ? "bg-gray-100 dark:bg-gray-700" : ""} ${rtlClasses.dropdownItem}`}
                      >
                        <span>{t('english')} {language === "en" && <span className="text-primary">✓</span>}</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => language !== "ps" && toggleLanguage()}
                        className={`${language === "ps" ? "bg-gray-100 dark:bg-gray-700" : ""} ${rtlClasses.dropdownItem}`}
                      >
                        <span>{t('pashto')} {language === "ps" && <span className="text-primary">✓</span>}</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Toggle Dark Mode */}
                  <button
                    onClick={() => setDarkMode(!darkMode)}
                    className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
                    aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
                  >
                    {darkMode ? <Sun size={18} /> : <Moon size={18} />}
                  </button>

                  {/* Notifications */}
                  <DropdownMenu>
                    <DropdownMenuTrigger className="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
                      <Bell size={18} />
                      {(unreadCount + getUnreadContactCount()) > 0 && (
                        <BadgeComponent className="absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center">
                          {unreadCount + getUnreadContactCount()}
                        </BadgeComponent>
                      )}
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-80" align="end">
                      <DropdownMenuLabel
                        className="flex items-center justify-between"
                        dir={language === "ps" ? "rtl" : "ltr"}
                      >
                        <span>{language === "ps" ? "خبرتیاوې" : "Notifications"}</span>
                        {(unreadCount + getUnreadContactCount()) > 0 && (
                          <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                            {unreadCount + getUnreadContactCount()} {language === "ps" ? "نوی" : "new"}
                          </span>
                        )}
                      </DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      {(notifications.length > 0 || contactNotifications.length > 0) ? (
                        <>
                          {/* Contact Notifications */}
                          {contactNotifications.slice(0, 3).map((contact) => (
                            <DropdownMenuItem
                              key={`contact-${contact.C_Id}`}
                              onClick={() => {
                                markContactAsRead(contact.C_Id);
                                navigate('/admin/notifications');
                              }}
                              className={`py-3 px-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer ${!readContacts.has(contact.C_Id) ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                            >
                              <div className="flex flex-col w-full">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    {!readContacts.has(contact.C_Id) && (
                                      <span style={{ fontSize: '8px' }}>🟢</span>
                                    )}
                                    <span className="font-medium text-sm">
                                      {contact.user_name}
                                    </span>
                                  </div>
                                  <span className="text-xs text-gray-400">Contact</span>
                                </div>
                                <span className="text-gray-500 dark:text-gray-400 text-xs mt-1">
                                  {contact.C_Title}
                                </span>
                                <span className="text-gray-400 dark:text-gray-500 text-xs mt-1">
                                  {contact.user_email}
                                </span>
                              </div>
                            </DropdownMenuItem>
                          ))}

                          {/* System Notifications */}
                          {notifications.slice(0, 2).map((n) => (
                            <DropdownMenuItem
                              key={n.id}
                              onClick={() => handleNotificationClick(n)}
                              className={`py-3 px-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer ${!n.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
                            >
                              <div className="flex flex-col">
                                <div className="flex items-center justify-between">
                                  <span className="font-medium text-sm">{n.title || n.message}</span>
                                  {!n.read && <div className="w-2 h-2 bg-blue-500 rounded-full"></div>}
                                </div>
                                <span className="text-gray-500 dark:text-gray-400 text-xs mt-1">{n.message}</span>
                                <span className="text-gray-400 dark:text-gray-500 text-xs mt-2">
                                  {new Date(n.timestamp).toLocaleString()}
                                </span>
                              </div>
                            </DropdownMenuItem>
                          ))}

                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            className="py-2 text-center text-primary text-sm font-medium cursor-pointer"
                            onClick={() => navigate('/admin/notifications')}
                          >
                            {language === "ps" ? "ټولې خبرتیاوې وګورئ" : "View all notifications"}
                          </DropdownMenuItem>
                        </>
                      ) : (
                        <div className="py-8 text-center">
                          <Bell size={40} className="mx-auto text-gray-300 dark:text-gray-600 mb-3" />
                          <p className="text-gray-500 dark:text-gray-400 text-sm">
                            {language === "ps" ? "هیڅ خبرتیا نشته" : "No notifications yet"}
                          </p>
                        </div>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Profile */}
                  <DropdownMenu>
                    <DropdownMenuTrigger className="flex items-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 p-1.5">
                      <AvatarComponent className="h-8 w-8 border-2 border-white dark:border-gray-800">
                      <AvatarImage
                            src={
                              userData?.image
                                ? `http://localhost:5432/public/images/users/${userData.image}`
                                : "/avatars/01.png"
                            }
                            alt="User"
                          />

                        <AvatarFallback className="bg-primary text-white">{getUserInitials()}</AvatarFallback>
                      </AvatarComponent>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="" align="end">
                      <div className="p-2 border-b border-gray-100 dark:border-gray-800">
                        <div className="flex items-center gap-3 p-2">
                          <AvatarComponent className="h-10 w-10">
                           <AvatarImage
                            src={
                              userData?.image
                                ? `http://localhost:5432/public/images/users/${userData.image}`
                                : "/avatars/01.png"
                            }
                            alt="User"
                          />

                            <AvatarFallback className="bg-primary text-white">{getUserInitials()}</AvatarFallback>
                          </AvatarComponent>
                          <div className="flex flex-col ">
                            <p className="text-sm font-medium">
                              {user?.firstName} {user?.lastName}
                            </p>
                            <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</p>
                          </div>
                        </div>
                      </div>
                      <DropdownMenuItem
                        onClick={handleProfileClick}
                        className="py-2 my-1"
                        dir={language === "ps" ? "rtl" : "ltr"}
                      >
                        <User className={language === "ps" ? "ml-3" : "mr-3"} size={16} />
                        <span>{language === "ps" ? "پروفایل" : "Profile"}</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={handleLogout}
                        className="py-2 my-1 text-red-600 dark:text-red-400"
                        dir={language === "ps" ? "rtl" : "ltr"}
                      >
                        <LogOut className={language === "ps" ? "ml-3" : "mr-3"} size={16} />
                        <span>{language === "ps" ? "وتل" : "Logout"}</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>
          </header>

          {/* Page Content */}
          <main className="p-6">{children}</main>
        </div>
      </div>
    </div>
  )
}

/////////////////////////////

//////////////////////
// "use client"

// import { useState, useEffect } from "react"
// import { useNavigate } from "react-router-dom"
// import {
//   Search,
//   Bell,
//   ChevronDown,
//   User,
//   Settings,
//   LogOut,
//   Moon,
//   Sun,
//   MenuIcon,
//   Calendar,
//   HelpCircle,
// } from "lucide-react"
// import { Badge as BadgeComponent } from "@/components/management-system/ui/Badge"
// import { Avatar as AvatarComponent, AvatarImage, AvatarFallback } from "@/components/management-system/ui/Avatar"
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from "@/components/management-system/ui/Dropdown-menu"
// import SideBar from "../components/admin/sidebar"
// import { useAuth } from "../contexts/AuthContext"
// import { useLanguage } from "../contexts/LanguageContext"
// import { useUser } from "../contexts/UsersContext"

// export default function AdminLayout({ children }) {
//   const [isSidebarOpen, setIsSidebarOpen] = useState(true)
//   const [searchQuery, setSearchQuery] = useState("")
//   const [notifications, setNotifications] = useState([])
//   const [isMobile, setIsMobile] = useState(false)
//   const [darkMode, setDarkMode] = useState(false)
//   const [currentDate, setCurrentDate] = useState(new Date())

//   const navigate = useNavigate()
//   const { user,logout } = useAuth()
//   const { language, toggleLanguage } = useLanguage()
//   const { userData } = useUser()



//   // Check mobile and set sidebar state
//   useEffect(() => {
//     const checkIfMobile = () => {
//       const mobile = window.innerWidth < 768
//       setIsMobile(mobile)
//       setIsSidebarOpen(!mobile) // Hide sidebar on mobile by default
//     }
//     checkIfMobile()
//     window.addEventListener("resize", checkIfMobile)
//     return () => window.removeEventListener("resize", checkIfMobile)
//   }, [])

//   // Set direction and language
//   useEffect(() => {
//     document.documentElement.dir = language === "ps" ? "rtl" : "ltr"
//     document.documentElement.lang = language
//   }, [language])

//   // Handle dark mode class on html element
//   useEffect(() => {
//     if (darkMode) {
//       document.documentElement.classList.add("dark")
//     } else {
//       document.documentElement.classList.remove("dark")
//     }
//   }, [darkMode])

//   // Update current date
//   useEffect(() => {
//     const timer = setInterval(() => setCurrentDate(new Date()), 60000)
//     return () => clearInterval(timer)
//   }, [])

//   const handleSearch = (e) => setSearchQuery(e.target.value)
//   const handleNotificationClick = (n) => console.log("Notification clicked:", n)
//   const handleProfileClick = () => navigate("/admin/profile")
//   const handleSettingsClick = () => navigate("/admin/settings")
//   const handleLogout = () => {
//     logout()
//     navigate("/signin")
//   }

//   const rtlClasses = {
//     sidebar: language === "ps" ? "right-0" : "left-0",
//     content: language === "ps" ? "mr-64" : "ml-64",
//     contentCollapsed: language === "ps" ? "" : "",
//     searchIcon: language === "ps" ? "right-3" : "left-3",
//     searchInput: language === "ps" ? "pr-10 pl-4" : "pl-10 pr-4",
//     dropdownItem: language === "ps" ? "flex-row-reverse" : "flex-row",
//     dropdownIcon: language === "ps" ? "ml-2" : "mr-2",
//   }

//   const getUserInitials = () => {
//     if (userData?.firstName && userData?.lastName) {
//       return `${userData.firstName[0]}${userData.lastName[0]}`.toUpperCase()
//     } else if (userData?.firstName) {
//       return userData.firstName[0].toUpperCase()
//     }
//     return ""
//   }

//   // Format date based on language
//   const formatDate = () => {
//     const options = { weekday: "long", year: "numeric", month: "long", day: "numeric" }
//     return currentDate.toLocaleDateString(language === "ps" ? "ps-AF" : "en-US", options)
//   }

//   // Toggle sidebar function
//   const toggleSidebar = () => {
//     setIsSidebarOpen(!isSidebarOpen)
//   }

//   return (
//     <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white`}>
//       <div className="flex">
//         {/* Mobile Sidebar Overlay */}
//         {isMobile && isSidebarOpen && (
//           <div
//             className="fixed inset-0 bg-black/50 z-40 backdrop-blur-sm"
//             onClick={() => setIsSidebarOpen(false)}
//             aria-hidden="true"
//           ></div>
//         )}

//         {/* Sidebar */}
//         <div
//           className={`fixed top-0 h-screen z-50 transition-all duration-300 ease-in-out ${
//             isSidebarOpen ? "translate-x-0" : language === "ps" ? "translate-x-full" : "-translate-x-full"
//           } ${rtlClasses.sidebar} ${isMobile ? "w-64" : "w-64"}`}
//         >
//           <SideBar isOpen={isSidebarOpen} onToggle={setIsSidebarOpen} darkMode={darkMode} />
//         </div>

//         {/* Content */}
//         <div
//           className={`flex-1 transition-all duration-300 ${
//             !isMobile && isSidebarOpen
//               ? rtlClasses.content
//               : !isMobile && !isSidebarOpen
//                 ? rtlClasses.contentCollapsed
//                 : "ml-0 mr-0"
//           }`}
//         >
//           {/* Header */}
//           <header className="bg-white dark:bg-gray-800 shadow-sm sticky top-0 z-30">
//             <div className="px-4 sm:px-6 lg:px-8">
//               <div className="flex items-center gap-2 justify-between h-16">
//                 {/* Left side with menu toggle and breadcrumb */}
//                 <div className="flex items-center gap-4">
//                   {/* Menu Toggle Button */}
//                   <button
//                     onClick={toggleSidebar}
//                     className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-primary/30"
//                     aria-label="Toggle sidebar"
//                   >
//                     <MenuIcon size={20} />
//                   </button>

//                   {/* Date Display */}
//                   <div
//                     className="hidden md:flex items-center  text-sm text-gray-500 dark:text-gray-400"
//                     dir={language === "ps" ? "rtl" : "ltr"}
//                   >
//                     <Calendar size={16} className={language === "ps" ? "ml-2" : "mr-2"} />
//                     <span>{formatDate()}</span>
//                   </div>
//                 </div>

//                 {/* Search Bar - Center */}
//                 <div className="flex-1 max-w-lg flex gap-2 mx-auto">
//                   <div className="relative">
//                     <Search
//                       className={`absolute ${language === "ps" ? "right-3" : "left-3"} top-1/2 transform -translate-y-1/2 text-gray-400`}
//                       size={18}
//                     />
//                     <input
//                       type="text"
//                       placeholder={language === "ps" ? "لټون..." : "Search..."}
//                       value={searchQuery}
//                       onChange={handleSearch}
//                       className={`w-full ${language === "ps" ? "pr-10 pl-4" : "pl-10 pr-4"} py-2 border border-gray-200 dark:border-gray-700 dark:bg-gray-800 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/30 bg-gray-50 dark:bg-gray-700 text-sm`}
//                       dir={language === "ps" ? "rtl" : "ltr"}
//                     />
//                   </div>
//                 </div>

//                 {/* Right Side */}
//                 <div className="flex items-center space-x-3">
//                   {/* Help Button */}
//                   <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
//                     <HelpCircle size={18} />
//                   </button>

//                   {/* Language Selector */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center space-x-1 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
//                       <span className="text-sm font-medium">{language === "en" ? "EN" : "PS"}</span>
//                       <ChevronDown size={14} />
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="w-40" sideOffset={5}>
//                       <DropdownMenuLabel className="text-center">
//                         {language === "ps" ? "ژبه وټاکئ" : "Select Language"}
//                       </DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem
//                         onClick={() => language !== "en" && toggleLanguage()}
//                         className={`${language === "en" ? "bg-gray-100 dark:bg-gray-700" : ""}`}
//                         dir="ltr"
//                       >
//                         <span>English {language === "en" && <span className="text-primary">✓</span>}</span>
//                       </DropdownMenuItem>
//                       <DropdownMenuItem
//                         onClick={() => language !== "ps" && toggleLanguage()}
//                         className={`${language === "ps" ? "bg-gray-100 dark:bg-gray-700" : ""}`}
//                         dir="rtl"
//                       >
//                         <span>پښتو {language === "ps" && <span className="text-primary">✓</span>}</span>
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Toggle Dark Mode */}
//                   <button
//                     onClick={() => setDarkMode(!darkMode)}
//                     className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300"
//                     aria-label={darkMode ? "Switch to light mode" : "Switch to dark mode"}
//                   >
//                     {darkMode ? <Sun size={18} /> : <Moon size={18} />}
//                   </button>

//                   {/* Notifications */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="relative p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-600 dark:text-gray-300">
//                       <Bell size={18} />
//                       {notifications.length > 0 && (
//                         <BadgeComponent className="absolute -top-1 -right-1 bg-primary text-white text-xs w-5 h-5 flex items-center justify-center">
//                           {notifications.length}
//                         </BadgeComponent>
//                       )}
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="w-80" align="end">
//                       <DropdownMenuLabel
//                         className="flex items-center justify-between"
//                         dir={language === "ps" ? "rtl" : "ltr"}
//                       >
//                         <span>{language === "ps" ? "خبرتیاوې" : "Notifications"}</span>
//                         {notifications.length > 0 && (
//                           <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
//                             {notifications.length} {language === "ps" ? "نوی" : "new"}
//                           </span>
//                         )}
//                       </DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       {notifications.length > 0 ? (
//                         <>
//                           {notifications.map((n) => (
//                             <DropdownMenuItem
//                               key={n.id}
//                               onClick={() => handleNotificationClick(n)}
//                               className="py-3 px-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer"
//                             >
//                               <div className="flex flex-col">
//                                 <span className="font-medium text-sm">{n.title}</span>
//                                 <span className="text-gray-500 dark:text-gray-400 text-xs mt-1">{n.message}</span>
//                                 <span className="text-gray-400 dark:text-gray-500 text-xs mt-2">{n.time}</span>
//                               </div>
//                             </DropdownMenuItem>
//                           ))}
//                           <DropdownMenuSeparator />
//                           <DropdownMenuItem className="py-2 text-center text-primary text-sm font-medium cursor-pointer">
//                             {language === "ps" ? "ټولې خبرتیاوې وګورئ" : "View all notifications"}
//                           </DropdownMenuItem>
//                         </>
//                       ) : (
//                         <div className="py-8 text-center">
//                           <Bell size={40} className="mx-auto text-gray-300 dark:text-gray-600 mb-3" />
//                           <p className="text-gray-500 dark:text-gray-400 text-sm">
//                             {language === "ps" ? "هیڅ خبرتیا نشته" : "No notifications yet"}
//                           </p>
//                         </div>
//                       )}
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Profile */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 p-1.5">
//                       <AvatarComponent className="h-8 w-8 border-2 border-white dark:border-gray-800">
//                         <AvatarImage src={userData?.profileImage || "/avatars/01.png"} alt="User" />
//                         <AvatarFallback className="bg-primary text-white">{getUserInitials()}</AvatarFallback>
//                       </AvatarComponent>
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="" align="end">
//                       <div className="p-2 border-b border-gray-100 dark:border-gray-800">
//                         <div className="flex items-center gap-3 p-2">
//                           <AvatarComponent className="h-10 w-10">
//                             <AvatarImage src={userData?.profileImage || "/avatars/01.png"} alt="User" />
//                             <AvatarFallback className="bg-primary text-white">{getUserInitials()}</AvatarFallback>
//                           </AvatarComponent>
//                           <div className="flex flex-col ">
//                             <p className="text-sm font-medium">
//                               {user?.firstName} {user?.lastName}
//                             </p>
//                             <p className="text-xs text-gray-500 dark:text-gray-400">{user?.email}</p>
//                           </div>
//                         </div>
//                       </div>
//                       <DropdownMenuItem
//                         onClick={handleProfileClick}
//                         className="py-2 my-1"
//                         dir={language === "ps" ? "rtl" : "ltr"}
//                       >
//                         <User className={language === "ps" ? "ml-3" : "mr-3"} size={16} />
//                         <span>{language === "ps" ? "پروفایل" : "Profile"}</span>
//                       </DropdownMenuItem>
//                       <DropdownMenuItem
//                         onClick={handleSettingsClick}
//                         className="py-2 my-1"
//                         dir={language === "ps" ? "rtl" : "ltr"}
//                       >
//                         <Settings className={language === "ps" ? "ml-3" : "mr-3"} size={16} />
//                         <span>{language === "ps" ? "تنظیمات" : "Settings"}</span>
//                       </DropdownMenuItem>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem
//                         onClick={handleLogout}
//                         className="py-2 my-1 text-red-600 dark:text-red-400"
//                         dir={language === "ps" ? "rtl" : "ltr"}
//                       >
//                         <LogOut className={language === "ps" ? "ml-3" : "mr-3"} size={16} />
//                         <span>{language === "ps" ? "وتل" : "Logout"}</span>
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>
//                 </div>
//               </div>
//             </div>
//           </header>

//           {/* Page Content */}
//           <main className="p-6">{children}</main>
//         </div>
//       </div>
//     </div>
//   )
// }


///////////////////////////////
// 'use client';

// import { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { Search, Bell, ChevronDown, User, Settings, LogOut, Moon, Sun } from 'lucide-react';
// import { Badge as BadgeComponent } from '@/components/management-system/ui/Badge';
// import { Avatar as AvatarComponent, AvatarImage, AvatarFallback } from '@/components/management-system/ui/Avatar';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '@/components/management-system/ui/Dropdown-menu';
// import SideBar from '../components/admin/sidebar';
// import { useAuth } from '../contexts/AuthContext';
// import { useLanguage } from '../contexts/LanguageContext';
// import { useUser } from '../contexts/UsersContext';

// export default function AdminLayout({ children }) {
//   const [isSidebarOpen, setIsSidebarOpen] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [notifications, setNotifications] = useState([]);
//   const [isMobile, setIsMobile] = useState(false);
//   const [darkMode, setDarkMode] = useState(false);

//   const navigate = useNavigate();
//   const { logout } = useAuth();
//   const { language, toggleLanguage } = useLanguage();
//   const { userData } = useUser();

//   // Check mobile
//   useEffect(() => {
//     const checkIfMobile = () => {
//       const mobile = window.innerWidth < 768;
//       setIsMobile(mobile);
//       setIsSidebarOpen(!mobile);
//     };
//     checkIfMobile();
//     window.addEventListener('resize', checkIfMobile);
//     return () => window.removeEventListener('resize', checkIfMobile);
//   }, []);

//   // Set direction and language
//   useEffect(() => {
//     document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
//     document.documentElement.lang = language;
//   }, [language]);

//   // Handle dark mode class on html element
//   useEffect(() => {
//     if (darkMode) {
//       document.documentElement.classList.add('dark');
//     } else {
//       document.documentElement.classList.remove('dark');
//     }
//   }, [darkMode]);

//   const handleSearch = (e) => setSearchQuery(e.target.value);
//   const handleNotificationClick = (n) => console.log('Notification clicked:', n);
//   const handleProfileClick = () => navigate('/admin/profile');
//   const handleSettingsClick = () => navigate('/admin/settings');
//   const handleLogout = () => {
//     logout();
//     navigate('/signin');
//   };

//   const rtlClasses = {
//     sidebar: language === 'ps' ? 'right-0' : 'left-0',
//     content: language === 'ps' ? 'mr-64' : 'ml-64',
//     contentCollapsed: language === 'ps' ? 'mr-20' : 'ml-20',
//     searchIcon: language === 'ps' ? 'right-3' : 'left-3',
//     searchInput: language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4',
//     dropdownItem: language === 'ps' ? 'flex-row-reverse' : 'flex-row',
//     dropdownIcon: language === 'ps' ? 'ml-2' : 'mr-2',
//   };

//   const getUserInitials = () => {
//     if (userData.firstName && userData.lastName) {
//       return `${userData.firstName[0]}${userData.lastName[0]}`.toUpperCase();
//     } else if (userData.firstName) {
//       return userData.firstName[0].toUpperCase();
//     }
//     return '';
//   };

//   return (
//     <div className={`min-h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-white`}>
//       <div className="flex">
//         {/* Sidebar */}
//         <div
//           className={`fixed top-0 h-screen transition-all duration-300 ${
//             isSidebarOpen ? 'w-64' : 'w-20'
//           } ${rtlClasses.sidebar}`}
//         >
//           <SideBar isOpen={isSidebarOpen} onToggle={() => setIsSidebarOpen(!isSidebarOpen)} darkMode={darkMode} />
//         </div>

//         {/* Content */}
//         <div
//           className={`flex-1 transition-all duration-300 ${
//             isSidebarOpen ? rtlClasses.content : rtlClasses.contentCollapsed
//           }`}
//         >
//           {/* Header */}
//           <header className="bg-white dark:bg-gray-800 shadow-sm">
//             <div className="px-4 sm:px-6 lg:px-8">
//               <div className="flex items-center justify-between h-16">
//                 {/* Search */}
//                 <div className="flex-1 max-w-lg">
//                   <div className="relative">
//                     <Search
//                       className={`absolute ${rtlClasses.searchIcon} top-1/2 transform -translate-y-1/2 text-gray-400`}
//                       size={20}
//                     />
//                     <input
//                       type="text"
//                       placeholder={language === 'ps' ? 'لټون...' : 'Search...'}
//                       value={searchQuery}
//                       onChange={handleSearch}
//                       className={`w-full ${rtlClasses.searchInput} py-2 border border-gray-300 dark:border-gray-700 dark:bg-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500`}
//                     />
//                   </div>
//                 </div>

//                 {/* Right Side */}
//                 <div className="flex items-center space-x-4">
//                   {/* Language Selector */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
//                       <span className="text-sm font-medium">{language === 'en' ? 'EN' : 'PS'}</span>
//                       <ChevronDown size={16} />
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="w-40" sideOffset={5}>
//                       <DropdownMenuLabel className="text-center">
//                         {language === 'ps' ? 'ژبه وټاکئ' : 'Select Language'}
//                       </DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem
//                         onClick={() => language !== 'en' && toggleLanguage()}
//                         className={`${language === 'en' ? 'bg-gray-100 dark:bg-gray-700' : ''}`}
//                       >
//                         <span className={rtlClasses.dropdownItem}>
//                           English {language === 'en' && <span className="text-blue-500">✓</span>}
//                         </span>
//                       </DropdownMenuItem>
//                       <DropdownMenuItem
//                         onClick={() => language !== 'ps' && toggleLanguage()}
//                         className={`${language === 'ps' ? 'bg-gray-100 dark:bg-gray-700' : ''}`}
//                       >
//                         <span className={rtlClasses.dropdownItem}>
//                           پښتو {language === 'ps' && <span className="text-blue-500">✓</span>}
//                         </span>
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Toggle Dark Mode */}
//                   <button
//                     onClick={() => setDarkMode(!darkMode)}
//                     className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
//                   >
//                     {darkMode ? <Sun size={18} /> : <Moon size={18} />}
//                   </button>

//                   {/* Notifications */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="relative p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
//                       <Bell size={20} />
//                       {notifications.length > 0 && (
//                         <BadgeComponent className="absolute -top-1 -right-1 bg-red-500 text-white text-xs">
//                           {notifications.length}
//                         </BadgeComponent>
//                       )}
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="w-80">
//                       <DropdownMenuLabel>{language === 'ps' ? 'خبرتیاوې' : 'Notifications'}</DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       {notifications.length > 0 ? (
//                         notifications.map((n) => (
//                           <DropdownMenuItem key={n.id} onClick={() => handleNotificationClick(n)} className="py-2">
//                             {n.message}
//                           </DropdownMenuItem>
//                         ))
//                       ) : (
//                         <DropdownMenuItem className="py-2 text-gray-500">
//                           {language === 'ps' ? 'هیڅ خبرتیا نشته' : 'No notifications'}
//                         </DropdownMenuItem>
//                       )}
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Profile */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700">
//                       <AvatarComponent className="h-8 w-8">
//                         <AvatarImage src={userData.profileImage || '/avatars/01.png'} alt="User" />
//                         <AvatarFallback>{getUserInitials()}</AvatarFallback>
//                       </AvatarComponent>
//                       {!isMobile && <ChevronDown size={16} />}
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent className="w-56">
//                       <DropdownMenuLabel>{language === 'ps' ? 'زما حساب' : 'My Account'}</DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem onClick={handleProfileClick} className={rtlClasses.dropdownItem}>
//                         <User className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'پروفایل' : 'Profile'}
//                       </DropdownMenuItem>
//                       <DropdownMenuItem onClick={handleSettingsClick} className={rtlClasses.dropdownItem}>
//                         <Settings className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'تنظیمات' : 'Settings'}
//                       </DropdownMenuItem>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem onClick={handleLogout} className={`text-red-600 ${rtlClasses.dropdownItem}`}>
//                         <LogOut className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'وتل' : 'Logout'}
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>
//                 </div>
//               </div>
//             </div>
//           </header>

//           {/* Page Content */}
//           <main className="p-4">{children}</main>
//         </div>
//       </div>
//     </div>
//   );
// }




//////////////////////
// 'use client';

// import { useState, useEffect } from 'react';
// import { useNavigate } from 'react-router-dom';
// import { Search, Bell, ChevronDown, User, Settings, LogOut } from 'lucide-react';
// import { Badge as BadgeComponent } from '@/components/management-system/ui/Badge';
// import { Avatar as AvatarComponent, AvatarImage, AvatarFallback } from '@/components/management-system/ui/Avatar';
// import {
//   DropdownMenu,
//   DropdownMenuContent,
//   DropdownMenuItem,
//   DropdownMenuLabel,
//   DropdownMenuSeparator,
//   DropdownMenuTrigger,
// } from '@/components/management-system/ui/Dropdown-menu';
// import SideBar from '../components/admin/sidebar';
// import { useAuth } from '../contexts/AuthContext';
// import { useLanguage } from '../contexts/LanguageContext';
// import { useUser } from '../contexts/UsersContext';

// export default function AdminLayout({ children }) {
//   const [isSidebarOpen, setIsSidebarOpen] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [notifications, setNotifications] = useState([]);
//   const [isMobile, setIsMobile] = useState(false);
//   const navigate = useNavigate();
//   const { logout } = useAuth();
//   const { language, toggleLanguage } = useLanguage();
//   const { userData } = useUser();

//   // Check if we're on mobile
//   useEffect(() => {
//     const checkIfMobile = () => {
//       const mobile = window.innerWidth < 768;
//       setIsMobile(mobile);
//       setIsSidebarOpen(!mobile); // Close sidebar by default on mobile
//     };

//     // Initial check
//     checkIfMobile();

//     // Add event listener for window resize
//     window.addEventListener('resize', checkIfMobile);

//     // Cleanup
//     return () => window.removeEventListener('resize', checkIfMobile);
//   }, []);

//   // Set document direction based on language
//   useEffect(() => {
//     document.documentElement.dir = language === 'ps' ? 'rtl' : 'ltr';
//     document.documentElement.lang = language;
//   }, [language]);

//   const handleSearch = (e) => {
//     setSearchQuery(e.target.value);
//     // Implement search functionality
//   };

//   const handleNotificationClick = (notification) => {
//     // Handle notification click
//     console.log('Notification clicked:', notification);
//   };

//   const handleProfileClick = () => {
//     navigate('/admin/profile');
//   };

//   const handleSettingsClick = () => {
//     navigate('/admin/settings');
//   };

//   const handleLogout = () => {
//     logout();
//     navigate('/signin');
//   };

//   // RTL/LTR specific classes
//   const rtlClasses = {
//     sidebar: language === 'ps' ? 'right-0' : 'left-0',
//     content: language === 'ps' ? 'mr-64' : 'ml-64',
//     contentCollapsed: language === 'ps' ? 'mr-20' : 'ml-20',
//     searchIcon: language === 'ps' ? 'right-3' : 'left-3',
//     searchInput: language === 'ps' ? 'pr-10 pl-4' : 'pl-10 pr-4',
//     dropdownContent: language === 'ps' ? 'left-0' : 'right-0',
//     dropdownItem: language === 'ps' ? 'flex-row-reverse' : 'flex-row',
//     dropdownIcon: language === 'ps' ? 'ml-2' : 'mr-2',
//   };

//   // Get user initials for avatar fallback
//   const getUserInitials = () => {
//     if (userData.firstName && userData.lastName) {
//       return `${userData.firstName.charAt(0)}${userData.lastName.charAt(0)}`.toUpperCase();
//     } else if (userData.firstName) {
//       return userData.firstName.charAt(0).toUpperCase();
//     }
//     return '';
//   };

//   return (
//     <div className={`min-h-screen bg-gray-100 ${language === 'ps' ? 'rtl' : 'ltr'}`}>
//       <div className="flex">
//         {/* Sidebar */}
//         <div
//           className={`fixed top-0 h-screen transition-all duration-300 ${
//             isSidebarOpen ? 'w-64' : 'w-20'
//           } ${rtlClasses.sidebar}`}
//         >
//           <SideBar isOpen={isSidebarOpen} onToggle={() => setIsSidebarOpen(!isSidebarOpen)} />
//         </div>

//         {/* Main Content */}
//         <div
//           className={`flex-1 transition-all duration-300 ${
//             isSidebarOpen ? rtlClasses.content : rtlClasses.contentCollapsed
//           }`}
//         >
//           {/* Header */}
//           <header className="bg-white shadow-sm">
//             <div className="px-4 sm:px-6 lg:px-8">
//               <div className="flex items-center justify-between h-16">
//                 {/* Search Bar */}
//                 <div className="flex-1 max-w-lg">
//                   <div className="relative">
//                     <Search
//                       className={`absolute ${rtlClasses.searchIcon} top-1/2 transform -translate-y-1/2 text-gray-400`}
//                       size={20}
//                     />
//                     <input
//                       type="text"
//                       placeholder={language === 'ps' ? 'لټون...' : 'Search...'}
//                       value={searchQuery}
//                       onChange={handleSearch}
//                       className={`w-full ${rtlClasses.searchInput} py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent`}
//                     />
//                   </div>
//                 </div>

//                 {/* Right side buttons */}
//                 <div className="flex items-center space-x-4">
//                   {/* Language Dropdown */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100">
//                       <span className="text-sm font-medium text-gray-700">{language === 'en' ? 'EN' : 'PS'}</span>
//                       <ChevronDown size={16} className="text-gray-600" />
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'} className="w-40" sideOffset={5}>
//                       <DropdownMenuLabel className="text-center">
//                         {language === 'ps' ? 'ژبه وټاکئ' : 'Select Language'}
//                       </DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem
//                         onClick={() => language !== 'en' && toggleLanguage()}
//                         className={`flex items-center justify-between ${language === 'en' ? 'bg-gray-100' : ''}`}
//                       >
//                         <span className={rtlClasses.dropdownItem}>
//                           {language === 'ps' ? 'انګلیسي' : 'English'}
//                           {language === 'en' && <span className="text-blue-500">✓</span>}
//                         </span>
//                       </DropdownMenuItem>
//                       <DropdownMenuItem
//                         onClick={() => language !== 'ps' && toggleLanguage()}
//                         className={`flex items-center justify-between ${language === 'ps' ? 'bg-gray-100' : ''}`}
//                       >
//                         <span className={rtlClasses.dropdownItem}>
//                           پښتو
//                           {language === 'ps' && <span className="text-blue-500">✓</span>}
//                         </span>
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Notifications */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="relative p-2 rounded-full hover:bg-gray-100">
//                       <Bell size={20} className="text-gray-600" />
//                       {notifications.length > 0 && (
//                         <BadgeComponent className="absolute -top-1 -right-1 bg-red-500 text-white text-xs">
//                           {notifications.length}
//                         </BadgeComponent>
//                       )}
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'} className="w-80">
//                       <DropdownMenuLabel>{language === 'ps' ? 'خبرتیاوې' : 'Notifications'}</DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       {notifications.length > 0 ? (
//                         notifications.map((notification) => (
//                           <DropdownMenuItem
//                             key={notification.id}
//                             onClick={() => handleNotificationClick(notification)}
//                             className="py-2"
//                           >
//                             {notification.message}
//                           </DropdownMenuItem>
//                         ))
//                       ) : (
//                         <DropdownMenuItem className="py-2 text-gray-500">
//                           {language === 'ps' ? 'هیڅ خبرتیا نشته' : 'No notifications'}
//                         </DropdownMenuItem>
//                       )}
//                     </DropdownMenuContent>
//                   </DropdownMenu>

//                   {/* Profile Dropdown */}
//                   <DropdownMenu>
//                     <DropdownMenuTrigger className="flex items-center space-x-2 p-2 rounded-full hover:bg-gray-100">
//                       <AvatarComponent className="h-8 w-8">
//                         <AvatarImage src={userData.profileImage || '/avatars/01.png'} alt="User" />
//                         <AvatarFallback>{getUserInitials()}</AvatarFallback>
//                       </AvatarComponent>
//                       {!isMobile && <ChevronDown size={16} className="text-gray-600" />}
//                     </DropdownMenuTrigger>
//                     <DropdownMenuContent align={language === 'ps' ? 'start' : 'end'} className="w-56">
//                       <DropdownMenuLabel>{language === 'ps' ? 'زما حساب' : 'My Account'}</DropdownMenuLabel>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem onClick={handleProfileClick} className={rtlClasses.dropdownItem}>
//                         <User className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'پروفایل' : 'Profile'}
//                       </DropdownMenuItem>
//                       <DropdownMenuItem onClick={handleSettingsClick} className={rtlClasses.dropdownItem}>
//                         <Settings className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'تنظیمات' : 'Settings'}
//                       </DropdownMenuItem>
//                       <DropdownMenuSeparator />
//                       <DropdownMenuItem onClick={handleLogout} className={`text-red-600 ${rtlClasses.dropdownItem}`}>
//                         <LogOut className={rtlClasses.dropdownIcon} size={16} />
//                         {language === 'ps' ? 'وتل' : 'Logout'}
//                       </DropdownMenuItem>
//                     </DropdownMenuContent>
//                   </DropdownMenu>
//                 </div>
//               </div>
//             </div>
//           </header>

//           {/* Main Content */}
//           <main className="">{children}</main>
//         </div>
//       </div>
//     </div>
//   );
// }

