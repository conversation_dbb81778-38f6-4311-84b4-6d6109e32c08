import express from "express";
import NotificationController from "../controllers/notificationController.js";
import { authenticate, authorizeAdmin } from "../middlewares/authMiddleware.js";

const router = express.Router();

// All notification routes require admin authentication
router.use(authenticate);
router.use(authorizeAdmin);

// Get all notifications
router.get("/", NotificationController.getAll);

// Get unread count
router.get("/unread-count", NotificationController.getUnreadCount);

// Get notifications by category
router.get("/category/:category", NotificationController.getByCategory);

// Get notification by ID
router.get("/:id", NotificationController.getById);

// Mark notification as read
router.patch("/:id/read", NotificationController.markAsRead);

// Mark all notifications as read
router.patch("/mark-all-read", NotificationController.markAllAsRead);

// Delete notification
router.delete("/:id", NotificationController.delete);

// Delete all notifications
router.delete("/", NotificationController.deleteAll);

// Create notification (for testing)
router.post("/", NotificationController.create);

// Test endpoint to create a sample contact notification
router.post("/test-contact", NotificationController.createTestContactNotification);

export default router;
